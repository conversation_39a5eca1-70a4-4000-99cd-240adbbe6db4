package net.xplife.system.community.interfaces.simplepicture;

import net.xplife.system.community.entity.simplepicture.SimplePicture;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface SimplePictureService {
    @RequestMapping(value = "/community/v1/simplepicture/getpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<SimplePicture> getPage(@RequestParam("type") String type,@RequestParam("sizeType") String sizeType, @RequestParam("pageno") int pageNo, @RequestParam("pagesize") int pageSize);

    @RequestMapping(value = "/community/v1/simplepicture/gettype", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<Map<String, String>> getType();

    @RequestMapping(value = "/community/v1/simplepicture/save", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public SimplePicture save(@RequestBody SimplePicture simplePicture);

    @RequestMapping(value = "/community/v1/simplepicture/delete", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void delete(@RequestParam("id") String id);
}
