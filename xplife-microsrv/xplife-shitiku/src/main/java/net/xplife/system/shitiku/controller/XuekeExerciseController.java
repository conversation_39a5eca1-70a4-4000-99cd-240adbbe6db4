package net.xplife.system.shitiku.controller;

import net.xplife.system.shitiku.service.XuekeBuService;
import net.xplife.system.shitiku.service.XuekeExerciseService;
import net.xplife.system.shitiku.utils.Constant;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.core.BaseController;
import net.xplife.system.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author：Chenjy
 * Date:2025/7/26
 * Description:
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/xueke/exercise")
public class XuekeExerciseController extends BaseController {

    @Autowired
    private XuekeExerciseService xuekeExerciseService;


//    @Autowired
//    private MemberOrderBuService memberOrderBuService;

    /***
     * 下载试卷
     * @return
     */
    @RequestMapping(value = "/downloadByPaperIdEnc")
    public ReturnDto downloadByPaperIdEnc() {
        try {
            String paperId = this.getValue("id");
            Boolean with_answer = this.getBooleanValue("with_answer");
            Boolean with_stem = this.getBooleanValue("with_stem");
            String client_user_id = this.getValue("client_user_id");
            String file_name = this.getValue("file_name");
            Boolean with_exp = this.getBooleanValue("with_exp");
            Integer answer_position = this.getIntValue("answer_position") == -1 ? null : this.getIntValue("answer_position");
            Boolean with_answer_area = this.getBooleanValue("with_answer_area");
            String file_format = this.getValue("file_format").isEmpty() ? "docx" : this.getValue("file_format");

            String userId = this.getValue("userid");
//            ToolsKit.Thread.execute(new Runnable() {
//                @Override
//                public void run() {
//                    memberOrderBuService.insertPaperViewHistory(paperIdEnc, userId);
//                }
//            });

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, xuekeExerciseService.paperDownload(paperId, with_answer, with_stem, client_user_id, file_name, with_exp, answer_position, with_answer_area, file_format));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取试卷下载地址
     * @return
     */
    @RequestMapping(value = "/getUrlByPaperIdEnc")
    public ReturnDto getUrlByPaperIdEnc() {
        try {
            String paperId = this.getValue("id");
            Boolean with_answer = this.getBooleanValue("with_answer");
            Boolean with_stem = this.getBooleanValue("with_stem");
            String client_user_id = this.getValue("client_user_id");
            String file_name = this.getValue("file_name");
            Boolean with_exp = this.getBooleanValue("with_exp");
            Integer answer_position = this.getIntValue("answer_position") == -1 ? null : this.getIntValue("answer_position");
            Boolean with_answer_area = this.getBooleanValue("with_answer_area");
            String file_format = this.getValue("file_format").isEmpty() ? "docx" : this.getValue("file_format");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, xuekeExerciseService.paperDownloadByPdfUrl(paperId, with_answer, with_stem, client_user_id, file_name, with_exp, answer_position, with_answer_area, file_format));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取试卷详情（QuanPin格式）
     * @return
     */
    @RequestMapping(value = "/findpaperdetails")
    public ReturnDto findPaperDetails() {
        try {
            // 必填参数
            String formula_pic_format = this.getValue("formula_pic_format");
            String client_user_id = this.getValue("client_user_id");
            String paperId = this.getValue("paperId");

            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    xuekeExerciseService.findPaperDetailsQuanPinFormat(formula_pic_format, client_user_id, paperId));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取试卷列表
     * @return
     */
    @RequestMapping(value = "/findpaperslist")
    public ReturnDto findPapersList() {
        try {
            // QuanPin格式参数（所有参数都是非必填）
            String periodCode = this.getValue("periodCode");
            String subjectCode = this.getValue("courseId");
            String paperName = this.getValue("paperName");
            String versionCode = this.getValue("versionCode");
            String gradeCode = this.getValue("gradeCode");
            String chapterCode = this.getValue("chapterCode");
            String type = this.getValue("type");
            String year = this.getValue("year");
            String province = this.getValue("province");

            // 分页参数设置默认值
            Integer currPage = this.getIntValue("currPage") == -1 ? 1 : this.getIntValue("currPage");
            Integer pageSize = this.getIntValue("pageSize") == -1 ? 50 : this.getIntValue("pageSize");
            if (pageSize > 50) {
                pageSize = 50;
            }
            // 调用QuanPin格式的方法
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    xuekeExerciseService.findPapersListQuanPinFormat(
                            periodCode, subjectCode, paperName, versionCode, gradeCode,
                            chapterCode, type, year, province, currPage, pageSize));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取对应的同步练习类型
     * @return
     */
    @RequestMapping(value = "/findpapertypehomeworklist")
    public ReturnDto findPaperTypeListForHomework() {
        try {
            String subjectCode = this.getValue("stageId");
            String chapterCode = this.getValue("chapterCode");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    xuekeExerciseService.getPaperTypeHomeworkList(subjectCode, chapterCode));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取对应的阶段考试类型
     * @return
     */
    @RequestMapping(value = "/findpapertypesectionalexamlist")
    public ReturnDto findPaperTypeSectionalExamList() {
        try {
            String subjectCode = this.getValue("stageId");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, xuekeExerciseService.getPaperTypeSectionalExamList(subjectCode));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取对应的升学考试类型
     * @return
     */
    @RequestMapping(value = "/findpapertypeelevenpluslist")
    public ReturnDto findPaperTypeElevenPlusList() {
        try {
            String subjectCode = this.getValue("stageId");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, xuekeExerciseService.getPaperTypeElevenPlusList(subjectCode));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取指定ID的行政区
     * @return
     */
    @RequestMapping(value = "/areas-by-id")
    public ReturnDto areasById() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, xuekeExerciseService.areasById(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /***
     * 获取行政区列表
     * @return
     */
    @RequestMapping(value = "/areas-all")
    public ReturnDto areasAll() {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, xuekeExerciseService.areasAll());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    // 获取学年段和科目
    @RequestMapping(value = "/findbasesubjectobj")
    public ReturnDto getSubjects() {
        try {
            // 不需要传参，返回所有可用的学段和学科列表
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    xuekeExerciseService.getAllSubjects());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findversionobj")
    public ReturnDto getVersions() {
        try {
            String courseId = this.getValue("course_id");
            String gradeId = this.getValue("grade_id");
            String pageIndex = this.getValue("page_index");
            String pageSize = this.getValue("page_size");
            String versionId = this.getValue("version_id");
            String isUpgrade = this.getValue("isupgrade");

            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    xuekeExerciseService.getVersions(courseId, gradeId, pageIndex, pageSize, versionId, isUpgrade));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            System.out.println("----------->>>>>>>>报错啦！！！" + e.getMessage());
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findchapterlist")
    public ReturnDto getCatalogs() {
        try {
            String textbookId = this.getValue("textbook_id");
            String chapterCode = this.getValue("chapterCode");
            String courseId = this.getValue("courseId");
            String versionCode = this.getValue("versionCode");
            String gradeCode = this.getValue("gradeCode");
            String stageId = this.getValue("stageId");
            if(stageId.isEmpty()){
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR,"传递的参数有误！");
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    xuekeExerciseService.getChapterList(textbookId, chapterCode.isEmpty() ? "0" : chapterCode, courseId, versionCode, gradeCode, stageId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
