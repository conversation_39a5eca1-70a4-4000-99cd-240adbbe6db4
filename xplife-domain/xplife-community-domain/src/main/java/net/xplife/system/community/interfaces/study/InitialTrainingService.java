package net.xplife.system.community.interfaces.study;

import net.xplife.system.community.entity.study.InitialTrainingInfo;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface InitialTrainingService {
    /***
     * 获取分页
     * @param pageno
     * @param pagesize
     * @param type
     * @return
     */
    @RequestMapping(value = "/community/v1/initialtraining/findPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<InitialTrainingInfo> findPage(@RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize, @RequestParam("type") String type);

    /***
     * 删除
     * @param id
     */
    @RequestMapping(value = "/community/v1/initialtraining/delInitialTrainingInfo", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void delete(@RequestParam("id") String id);

    /**
     * 新增或修改
     * @param InitialTrainingInfo
     */
    @RequestMapping(value = "/community/v1/initialtraining/addInitialTrainingInfo", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public InitialTrainingInfo saveOrUpdate(@RequestBody InitialTrainingInfo InitialTrainingInfo);

}
