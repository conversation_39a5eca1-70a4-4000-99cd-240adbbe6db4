package net.xplife.system.platform.community.cache.feed;

import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.community.entity.feed.GiveLikeFeed;
import net.xplife.system.community.enums.feed.GiveLikeFeedCacheEnums;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点赞的动态缓存服务类
 * 
 * <AUTHOR> 2018年7月25日
 */
@Service
public class GiveLikeFeedCacheService {
    /**
     * 保存点赞的动态
     * 
     * @param giveLikeFeed
     */
    public void saveGiveLikeFeed(GiveLikeFeed giveLikeFeed) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_BY_ID.getKey() + giveLikeFeed.getId();
        CacheKit.cache().set(key, giveLikeFeed, ToolsConst.MONTH_SECOND);
    }

    /**
     * 获取点赞的动态
     * 
     * @param id
     *            记录ID
     * @return
     */
    public GiveLikeFeed getGiveLikeFeedById(String id) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_BY_ID.getKey() + id;
        return CacheKit.cache().get(key, GiveLikeFeed.class);
    }

    /**
     * 删除点赞的动态
     * 
     * @param id
     *            记录ID
     * @return
     */
    public void removeGiveLikeFeedById(String id) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_BY_ID.getKey() + id;
        CacheKit.cache().del(key);
    }

    /**
     * 保存点赞的动态列表
     * 
     * @param userId
     *            用户ID
     * @param id
     *            ID
     * @param time
     */
    public void addGiveLikeFeedIdToList(String userId, String  id, long time) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_ID_LIST.getKey() + userId;
        CacheKit.cache().zadd(key, time, id, ToolsConst.MONTH_SECOND);
    }

    /**
     * 从点赞的动态列表ID集合中删除ID
     * 
     * @param userId
     *            用户ID
     * @param id
     *            ID
     */
    public void removeGiveLikeFeedIdToList(String userId, String id) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_ID_LIST.getKey() + userId;
        CacheKit.cache().zrem(key, id);
    }

    /**
     * 获取动态ID所在集合列表中的位置
     * 
     * @param userId
     *            用户ID
     * @param id
     *            ID
     * @return
     */
    public Long zrevrank(String userId, String id) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_ID_LIST.getKey() + userId;
        return CacheKit.cache().zrevrank(key, id);
    }

    /**
     * 根据userId,获取所有的点赞记录
     *
     * @return
     */
    public List<String> getIdsByUserId(String userId) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_ID_LIST.getKey() + userId;
        return CacheKit.cache().zrevrank(key);
    }

    public List<String> getIdsPageByUserId(String userId, int page, int pageSize) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_ID_LIST.getKey() + userId;
        return CacheKit.cache().zrevrange(key, page, pageSize);
    }



    /**
     * 保存点赞的动态用户ID列表
     *
     * @param userId
     *            用户ID
     * @param time
     */
    public void addFeedIdToList(String feedId, String id, long time) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_RECORD_UID_LIST.getKey() + feedId;
        CacheKit.cache().zadd(key, time, id, ToolsConst.MONTH_SECOND);
    }

    /**
     * 从点赞的动态用户ID列表中删除用户ID
     *
     * @param id
     *            用户ID
     */
    public void removeIdToFeedList(String id,String feedId) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_RECORD_UID_LIST.getKey() + feedId;
        CacheKit.cache().zrem(key, id);
    }

    /**
     * 判断点赞的用户列表是否存在
     *
     * @return
     */
    public boolean existsFeedIdList(String feedId) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_RECORD_UID_LIST.getKey() + feedId;
        return CacheKit.cache().exists(key);
    }

    /**
     * 根据feedId,获取对应的点赞记录
     *
     * @return
     */
    public List<String> getIdsByFeedId(String feedId) {
        String key = GiveLikeFeedCacheEnums.GIVE_LIKE_FEED_RECORD_UID_LIST.getKey() + feedId;
        return CacheKit.cache().zrevrank(key);
    }
}
