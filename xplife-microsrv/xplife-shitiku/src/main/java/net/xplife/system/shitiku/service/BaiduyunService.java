package net.xplife.system.shitiku.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.xplife.system.http.kit.HttpKit;
import org.springframework.beans.factory.annotation.Value;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Author：Chenjy
 * Date:2025/8/27
 * Description:
 */
@Service
@RefreshScope
public class BaiduyunService {
    private static final Logger logger = LoggerFactory.getLogger(BaiduyunService.class);

    @Value("${baiduyun.oauth.appKey}")
    private String baiduyunOauthAppKey;

    @Value("${baiduyun.oauth.appSecret}")
    private String baiduyunOauthAppSecret;

    @Value("${baiduyun.oauth.host}")
    private String baiduyunOauthHost;

    @Value("${baiduyun.oauth.path}")
    private String baiduyunOauthPath;

    public String getToken() {
        try {
            Map<String, String> param = new HashMap<>();
            param.put("grant_type", "client_credentials");
            param.put("client_id", baiduyunOauthAppKey);
            param.put("client_secret", baiduyunOauthAppSecret);
            String url = baiduyunOauthHost + baiduyunOauthPath;
            String result = HttpKit.http().url(url).param(param).get().asString();
            JSONObject object = JSON.parseObject(result);
            if (object.containsKey("access_token")) {
                return object.getString("access_token");
            } else {
                return object.getString("error_description");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
