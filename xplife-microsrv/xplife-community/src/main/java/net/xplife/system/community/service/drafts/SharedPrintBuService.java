package net.xplife.system.community.service.drafts;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.xplife.system.account.dto.user.UserLoginDto;
import net.xplife.system.account.interfaces.user.UserAccountService;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.sysmsg.dto.GetMsgColumnDto;
import net.xplife.system.sysmsg.dto.MsgColumnDto;
import net.xplife.system.sysmsg.dto.SaveMsgColumnDto;
import net.xplife.system.sysmsg.enums.MsgColumnTypeEnums;
import net.xplife.system.sysmsg.interfaces.MsgCenterService;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.WebConst;
import net.xplife.system.web.config.CommonProperties;
import net.xplife.system.community.cache.drafts.SharedPrintCacheService;
import net.xplife.system.community.dao.drafts.SharedPrintDao;
import net.xplife.system.community.dto.drafts.ShareInfoDto;
import net.xplife.system.community.dto.drafts.SharedPrintDto;
import net.xplife.system.community.dto.drafts.SharedPrintInfoDto;
import net.xplife.system.community.entity.drafts.SharedPrint;
import net.xplife.system.community.enums.drafts.SharedPrintFromEnums;
import net.xplife.system.community.utils.Constant;
import cn.hutool.core.date.DateUnit;
@Service
public class SharedPrintBuService {
    @Autowired
    private SharedPrintDao          sharedPrintDao;
    @Autowired
    private SharedPrintCacheService sharedPrintCacheService;
    @Autowired
    private CommonProperties        commonProperties;
    @Autowired
    private PrintRecordBuService    printRecordBuService;
    @Autowired
    private MsgCenterService        msgCenterService;
    @Autowired
    private UserAccountService      userAccountService;

    public void save(SharedPrint sharedPrint) {
        sharedPrintDao.saveEntity(sharedPrint);
        sharedPrintCacheService.saveSharedPrint(sharedPrint);
    }

    public void addSharedPrintIdToList(String userId, String id, long time) {
        sharedPrintCacheService.addSharedPrintIdToList(userId, id, time);
    }

    /**
     * 根据ID获取共享打印对象
     * 
     * @param id
     *            ID
     * @return 共享打印对象
     * @throws ServiceException
     */
    public SharedPrint getSharedPrintById(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        SharedPrint sharedPrint = sharedPrintCacheService.getSharedPrint(id);
        if (ToolsKit.isEmpty(sharedPrint)) {
            try {
                sharedPrint = sharedPrintDao.getById(id, ToolsConst.DATA_SUCCESS_STATUS);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(sharedPrint)) {
                sharedPrintCacheService.saveSharedPrint(sharedPrint);
            }
        }
        return sharedPrint;
    }

    /**
     * 获取共享打印分享内容
     * 
     * @param userId
     *            用户ID
     * @param title
     *            标题
     * @param minute
     *            分钟
     * @return
     * @throws ServiceException
     */
    public ShareInfoDto getSharedPrintDto(String userId, String codeId, String title, String minute, String from) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(title)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标题内容不能为空");
        }
        if (ToolsKit.isEmpty(minute)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("时效不能为空");
        }
        if (ToolsKit.isEmpty(from)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("来源不能为空");
        }
        Date now = new Date();
        SharedPrint sharedPrint = new SharedPrint();
        ToolsKit.setIdEntityData(sharedPrint, userId);
        sharedPrint.setUserId(userId);
        sharedPrint.setCodeId(ToolsKit.isEmpty(codeId) ? 0 : Integer.parseInt(codeId));
        sharedPrint.setTitle(title);
        if (Integer.parseInt(minute) == -1) {
            sharedPrint.setValidityTime(ToolsKit.Date.offsetMonth(now, 100));
        } else {
            sharedPrint.setValidityTime(ToolsKit.Date.offsetMinute(now, Integer.parseInt(minute)));
        }
        sharedPrint.setTotalMinute(Integer.parseInt(minute));
        sharedPrint.setSendTime(now);
        sharedPrint.setRecoveryTime(now);
        sharedPrint.setFrom(from);
        sharedPrintDao.saveEntity(sharedPrint);
        sharedPrintCacheService.saveSharedPrint(sharedPrint);
        sharedPrintCacheService.addSharedPrintIdToList(userId, sharedPrint.getId(), sharedPrint.getRecoveryTime().getTime());
        printRecordBuService.savePublicRecord(sharedPrint.getId(), "互动主题：" + title);
        ShareInfoDto dto = new ShareInfoDto();
        dto.setTitle(title);
        dto.setContent(Constant.SHARE_CONTENT);
        dto.setIconUrl(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), Constant.SHARE_ICON_URL));
        dto.setShareUrl(ToolsKit.URL.getUrlByServer(commonProperties.getShareDomain() + commonProperties.getH5Env(), Constant.SHARE_INFO_URL) + "?id="
                + sharedPrint.getId());
        dto.setType(1);
        return dto;
    }

    /**
     * 初始化共享打印列表
     * 
     * @param userId
     *            用户ID
     * @return
     */
    public List<SharedPrint> initSharedPrintList(String userId) {
        List<SharedPrint> sharedPrintList = sharedPrintDao.findSharedPrintList(userId, 0, WebConst.DEFAULT_LIST_INT_COUNT, null);
        if (ToolsKit.isNotEmpty(sharedPrintList)) {
            ToolsKit.Thread.execute(new Runnable() {
                @Override
                public void run() {
                    for (SharedPrint sharedPrint : sharedPrintList) {
                        sharedPrintCacheService.addSharedPrintIdToList(userId, sharedPrint.getId(), sharedPrint.getRecoveryTime().getTime());
                    }
                }
            });
        }
        return sharedPrintList;
    }

    /**
     * 获取共享打印列表
     * 
     * @param userId
     *            用户ID
     * @param page
     *            当前页
     * @param pageSize
     *            每页大小
     * @param lastId
     *            最后一条数据ID
     * @return 共享打印ID列表
     */
    private List<String> getSharedPrintIdsList(String userId, int page, int pageSize, String lastId) {
        List<String> idsList = null;
        if (!sharedPrintCacheService.existsList(userId)) {
            List<SharedPrint> sharedPrintList = this.initSharedPrintList(userId);
            if (ToolsKit.isNotEmpty(sharedPrintList)) {
                idsList = new ArrayList<String>();
                boolean isOver = sharedPrintList.size() < (page * pageSize + pageSize);
                int end = page * pageSize + pageSize;
                if (isOver) {
                    end = sharedPrintList.size();
                }
                sharedPrintList = sharedPrintList.subList(page * pageSize, end);
                for (SharedPrint sharedPrint : sharedPrintList) {
                    idsList.add(sharedPrint.getId());
                }
            }
        } else {
            Long rank = null;
            if (ToolsKit.isNotEmpty(lastId)) {
                rank = sharedPrintCacheService.zrevrank(userId, lastId);
            }
            int start = 0;
            int end = 0;
            if (ToolsKit.isNotEmpty(rank)) {
                start = Integer.parseInt(String.valueOf(rank)) + 1;
                end = start + pageSize;
            } else {
                start = page * pageSize;
                end = page * pageSize + pageSize;
            }
            idsList = sharedPrintCacheService.getSharedPrintIdsList(userId, start, end);
            if (ToolsKit.isEmpty(idsList)) {
                List<SharedPrint> sharedPrintList = sharedPrintDao.findSharedPrintList(userId, page, pageSize, lastId);
                if (ToolsKit.isNotEmpty(sharedPrintList)) {
                    idsList = new ArrayList<String>();
                    for (SharedPrint sharedPrint : sharedPrintList) {
                        sharedPrintCacheService.addSharedPrintIdToList(userId, sharedPrint.getId(), sharedPrint.getRecoveryTime().getTime());
                        idsList.add(sharedPrint.getId());
                    }
                }
            }
        }
        return idsList;
    }

    /**
     * 获取共享打印列表
     * 
     * @param userId
     *            用户ID
     * @param pageNo
     * @param pageSize
     * @param lastId
     * @return
     * @throws ServiceException
     */
    public List<SharedPrintDto> getSharedPrintList(String userId, int pageNo, int pageSize, String lastId) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        List<SharedPrintDto> dtoList = new ArrayList<SharedPrintDto>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            List<String> idsList = this.getSharedPrintIdsList(userId, pageNo, pageSize, lastId);
            if (ToolsKit.isNotEmpty(idsList)) {
                Date now = new Date();
                Map<String, SharedPrintFromEnums> map = SharedPrintFromEnums.getMap();
                List<Integer> msgType = new ArrayList<Integer>();
                msgType.add(MsgColumnTypeEnums.SHARE.getType());
                List<MsgColumnDto> msgList = msgCenterService.getMsgColumnByType(new GetMsgColumnDto(userId, msgType));
                boolean isUpdate = false;
                for (String id : idsList) {
                    SharedPrint sharedPrint = this.getSharedPrintById(id);
                    if (ToolsKit.isNotEmpty(sharedPrint)) {
                        SharedPrintDto dto = new SharedPrintDto();
                        dto.setId(id);
                        dto.setTitle(sharedPrint.getTitle());
                        dto.setContent(map.get(sharedPrint.getFrom()).getContent());
                        dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), map.get(sharedPrint.getFrom()).getIcon()));
                        dto.setRecoveryTime(sharedPrint.getRecoveryTime());
                        dto.setTotalTime(sharedPrint.getTotalMinute());
                        int currentTime = (int) ToolsKit.Date.between(sharedPrint.getSendTime(), now, DateUnit.MINUTE);
                        if (currentTime > sharedPrint.getTotalMinute()) {
                            dto.setCurrentTime(sharedPrint.getTotalMinute());
                        } else {
                            dto.setCurrentTime(currentTime);
                        }
                        if (ToolsKit.isNotEmpty(msgList)) {
                            if (sharedPrint.getRecoveryTime().getTime() > msgList.get(0).getLastDate().getTime()) {
                                dto.setIsRed(ToolsConst.STATUS_1);
                                isUpdate = true;
                            }
                        } else {
                            dto.setIsRed(ToolsConst.STATUS_0);
                            // 发起互动打印后，未收到好友纸条时，文案显示为“已发起互动打印”
                            dto.setContent("已发起互动打印");
                        }
                        dtoList.add(dto);
                    }
                }
                if (isUpdate) {
                    ToolsKit.Thread.execute(new Runnable() {
                        @Override
                        public void run() {
                            SaveMsgColumnDto saveMsgColumnDto = new SaveMsgColumnDto();
                            saveMsgColumnDto.setUserId(userId);
                            saveMsgColumnDto.setType(MsgColumnTypeEnums.SHARE.getType());
                            saveMsgColumnDto.setCount(0);
                            saveMsgColumnDto.setLasdDate(now);
                            msgCenterService.saveMsgColumnByType(saveMsgColumnDto);
                        }
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }

    /**
     * 获取共享打印信息
     * 
     * @param shareId
     *            记录信息ID
     * @return
     * @throws ServiceException
     */
    public SharedPrintInfoDto getSharePrintInfo(String shareId) throws ServiceException {
        if (ToolsKit.isEmpty(shareId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        Date now = new Date();
        SharedPrint sharedPrint = this.getSharedPrintById(shareId);
        if (ToolsKit.isEmpty(sharedPrint) || now.getTime() > sharedPrint.getValidityTime().getTime()) {
            throw new ServiceException().setCode(ExceptionEnums.MATURITY.getCode()).setMessage("分享已失效");
        }
        UserLoginDto userLoginDto = userAccountService.getUserInfo(sharedPrint.getUserId());
        if (ToolsKit.isEmpty(userLoginDto) || ToolsKit.isEmpty(userLoginDto.getUserInfoDto())) {
            throw new ServiceException().setCode(ExceptionEnums.MATURITY.getCode()).setMessage("分享已失效");
        }
        SharedPrintInfoDto dto = new SharedPrintInfoDto();
        dto.setShareId(shareId);
        dto.setTitle(sharedPrint.getTitle());
        dto.setUserName(userLoginDto.getUserInfoDto().getNickName());
        dto.setUserPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), userLoginDto.getUserInfoDto().getUserPic()));
        dto.setValidityTime(sharedPrint.getValidityTime());
        return dto;
    }
}
