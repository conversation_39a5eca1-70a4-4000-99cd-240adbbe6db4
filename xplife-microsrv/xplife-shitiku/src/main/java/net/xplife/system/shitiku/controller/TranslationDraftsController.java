package net.xplife.system.shitiku.controller;

import net.xplife.system.mongo.common.Page;
import net.xplife.system.shitiku.dto.*;
import net.xplife.system.shitiku.entity.AIConversation;
import net.xplife.system.shitiku.entity.TranslationDrafts;
import net.xplife.system.shitiku.service.TranslationDraftsService;
import net.xplife.system.shitiku.utils.Constant;
import net.xplife.system.shitiku.vo.AIConversationVO;
import net.xplife.system.shitiku.vo.TranslationDraftsVO;
import net.xplife.system.web.core.BaseController;
import net.xplife.system.web.dto.ReturnDto;
import net.xplife.system.web.dto.UserInfoDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/translation/drafts")
@Validated
public class TranslationDraftsController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(TranslationDraftsController.class);

    @Autowired
    private TranslationDraftsService translationDraftsService;

    /**
     * 保存AI问答对话
     */
    @PostMapping
    public ReturnDto saveConversation(@Valid @RequestBody TranslationDraftsDTO dto) {
        logger.info("接收到保存文本翻译对话请求: {}", dto);

        try {
            UserInfoDto userInfoDto = this.getUserInfoDto();
            if (userInfoDto == null) {
                return returnFailJson(new RuntimeException("用户未登录!"));
            }
            dto.setUserId(userInfoDto.getUserId());

            TranslationDrafts conversation = translationDraftsService.saveTranslationDrafts(dto);
            // 转换为VO对象，避免直接返回实体
            TranslationDraftsVO vo = convertToVO(conversation);
            logger.info("保存文本翻译对话成功，ID:{}", conversation.getId());
            return returnSuccessJson(vo);
        } catch (Exception e) {
            logger.error("保存文本翻译对话失败: {}", e.getMessage(), e);
            return returnFailJson(e);
        }
    }

    /**
     * 获取文本翻译对话列表
     */
    @GetMapping
    public ReturnDto getConversations(TranslationDraftsQueryDTO queryDTO) {
        UserInfoDto userInfoDto = this.getUserInfoDto();
        if (userInfoDto == null) {
            return returnFailJson(new RuntimeException("用户未登录!"));
        }
        queryDTO.setUserId(userInfoDto.getUserId());

        // 设置默认分页数据
        if (queryDTO.getPageNum() == null || queryDTO.getPageNum() <= 0) {
            queryDTO.setPageNum(1);
        }
        if (queryDTO.getPageSize() == null || queryDTO.getPageSize() <= 0) {
            queryDTO.setPageSize(20);
        }
        logger.info("接收到获取文本翻译对话列表请求: {}", queryDTO);

        try {
            // 限制分页大小，防止大数据量查询
            if (queryDTO.getPageSize() > 100) {
                queryDTO.setPageSize(100);
                logger.warn("查询页面大小超限，已调整为最大值100");
            }

            Page<TranslationDrafts> conversations = translationDraftsService.findTranslationDrafts(queryDTO);
            // 转换为VO对象列表
            Page<TranslationDraftsVO> voPage = new Page<>();
            voPage.setPageNo(conversations.getPageNo());
            voPage.setPageSize(conversations.getPageSize());
            voPage.setTotalCount(conversations.getTotalCount());

            // 手动转换列表内容
            List<TranslationDraftsVO> voList = conversations.getResult().stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            voPage.setResult(voList);

            logger.info("查询文本翻译对话列表成功，结果数量:{}", conversations.getTotalCount());
            return returnSuccessJson(voPage);
        } catch (Exception e) {
            logger.error("查询文本翻译对话列表失败: {}", e.getMessage(), e);
            return returnFailJson(e);
        }
    }

    /**
     * 批量删除文本翻译对话
     */
    @PostMapping("/batch")
    public ReturnDto batchDeleteConversations(@Valid @RequestBody TranslationDraftsBatchDeleteDTO batchDeleteDTO) {
        logger.info("接收到批量删除AI问答对话请求: {}", batchDeleteDTO);

        try {
            translationDraftsService.batchDeleteTranslationDrafts(batchDeleteDTO);

            logger.info("批量删除文本翻译对话成功，删除ID列表:{}", batchDeleteDTO.getIds());
            return returnSuccessJson("删除成功");
        } catch (Exception e) {
            logger.error("批量删除文本翻译对话失败: {}", e.getMessage(), e);
            return returnFailJson(e);
        }
    }

    /**
     * 将实体对象转换为视图对象
     */
    private TranslationDraftsVO convertToVO(TranslationDrafts entity) {
        // 实际项目中应使用MapStruct等工具进行对象转换
        TranslationDraftsVO vo = new TranslationDraftsVO();
        // 设置需要返回的字段，避免敏感信息泄露
        vo.setId(entity.getId());
        // 设置其他需要的字段...
        vo.setUserId(entity.getUserId());
        vo.setTranslation(entity.getTranslation());
        vo.setOriginal(entity.getOriginal());

        // 定义标准格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (entity.getCreateTime() != null) {
            vo.setCreateTime(sdf.format(entity.getCreateTime()));
        }
        if (entity.getUpdateTime() != null) {
            vo.setUpdateTime(sdf.format(entity.getUpdateTime()));
        }

        return vo;
    }
}
