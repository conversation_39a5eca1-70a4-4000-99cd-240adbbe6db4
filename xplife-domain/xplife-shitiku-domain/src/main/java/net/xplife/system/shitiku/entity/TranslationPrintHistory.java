package net.xplife.system.shitiku.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import net.xplife.system.shitiku.vo.TranslationPrintHistoryDataVO;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
@Data
@Document(collection = "V1_TranslationPrintHistory")
public class TranslationPrintHistory implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final String USER_ID_FIELD = "userId";
    public static final String STATUS_FIELD = "status";
    public static final String CREATETIME_FIELD = "createtime";

    @Id
    private String id;

    /**
     * 用户ID
     */
    @NotNull
    @Indexed(name = "_userId_")
    private String userId;

    private Integer status; // 状态：0-正常，1-已删除

    /**
     * 打印内容
     **/
    private List<TranslationPrintHistoryDataVO> printData;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;
}
