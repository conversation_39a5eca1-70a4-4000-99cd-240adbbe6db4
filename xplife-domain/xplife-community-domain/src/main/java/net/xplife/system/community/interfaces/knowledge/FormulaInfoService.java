package net.xplife.system.community.interfaces.knowledge;

import net.xplife.system.community.dto.examinationresource.ExaminationResourceDto;
import net.xplife.system.community.entity.knowledge.FormulaInfo;
import net.xplife.system.community.entity.knowledge.FormulaKnowledge;
import net.xplife.system.community.entity.knowledge.FormulaSubject;
import net.xplife.system.community.entity.soulword.SoulWord;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface FormulaInfoService {
    @RequestMapping(value = "/community/v1/formulainfo/findgrades", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<Map<String, Object>> findGrades();


    /***
     * 获取分页
     * @param pageno
     * @param pagesize
     * @return
     */
    @RequestMapping(value = "/community/v1/formulainfo/findFormulaInfoPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<FormulaInfo> findFormulaInfoPage(@RequestParam("pageno") int pageno,
                                      @RequestParam("pagesize") int pagesize,
                                      @RequestParam("gradeId") String gradeId,
                                      @RequestParam("subjectId") String subjectId,
                                      @RequestParam("knowledgeName") String knowledgeName);

    @RequestMapping(value = "/community/v1/formulainfo/findFormulaInfoById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public FormulaInfo findFormulaInfoById(@RequestParam("id") String id);

    @RequestMapping(value = "/community/v1/formulainfo/addOrUpdateFormulaInfo", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void addOrUpdateFormulaInfo(@RequestBody FormulaInfo formulaInfo);

    @RequestMapping(value = "/community/v1/formulainfo/deleteFormulaInfo", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteFormulaInfoById(@RequestParam("id") String id);



    /***
     * 获取分页
     * @param pageno
     * @param pagesize
     * @return
     */
    @RequestMapping(value = "/community/v1/formulainfo/findFormulaKnowldegePage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<FormulaKnowledge> findFormulaKnowldegePage(@RequestParam("pageno") int pageno,
                                                 @RequestParam("pagesize") int pagesize,
                                                 @RequestParam("gradeId") String gradeId,
                                                 @RequestParam("subjectId") String subjectId);

    @RequestMapping(value = "/community/v1/formulainfo/findFormulaKnowledgeById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public FormulaKnowledge findFormulaKnowledgeById(@RequestParam("id") String id);

    @RequestMapping(value = "/community/v1/formulainfo/addOrUpdateFormulaKnowledge", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void addOrUpdateFormulaKnowledge(@RequestBody FormulaKnowledge formulaKnowledge);

    @RequestMapping(value = "/community/v1/formulainfo/deleteFormulaKnowledge", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteFormulaKnowledgeById(@RequestParam("id") String id);




    /***
     * 获取分页
     * @param pageno
     * @param pagesize
     * @return
     */
    @RequestMapping(value = "/community/v1/formulainfo/findFormulaSubjectPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<FormulaSubject> findFormulaSubjectPage(@RequestParam("pageno") int pageno,
                                                 @RequestParam("pagesize") int pagesize,
                                                 @RequestParam("gradeId") String gradeId,
                                                 @RequestParam("subjectId") String subjectId);

    @RequestMapping(value = "/community/v1/formulainfo/findFormulaSubjectById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public FormulaSubject findFormulaSubjectById(@RequestParam("id") String id);

    @RequestMapping(value = "/community/v1/formulainfo/addOrUpdateFormulaSubject", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void addOrUpdateFormulaSubject(@RequestBody FormulaSubject formulaSubject);

    @RequestMapping(value = "/community/v1/formulainfo/deleteFormulaSubject", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteFormulaSubjectById(@RequestParam("id") String id);

}
