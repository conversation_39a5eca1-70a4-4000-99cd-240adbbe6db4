package net.xplife.system.shitiku.controller;

import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.shitiku.utils.Constant;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.web.core.BaseController;
import net.xplife.system.web.dto.ReturnDto;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author：Chenjy
 * Date:2025/8/6
 * Description:
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/cache")
public class CacheOperController extends BaseController {

    @RequestMapping("/clear")
    public ReturnDto clearCache(String cacheName) {
        try {
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, CacheKit.cache().del(cacheName));
        } catch (Exception e) {
            return this.returnFailJson(e);
        }

    }
}
