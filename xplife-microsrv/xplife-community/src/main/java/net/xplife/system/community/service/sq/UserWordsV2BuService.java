package net.xplife.system.community.service.sq;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.xplife.system.community.cache.sq.UserWordsV2CacheService;
import net.xplife.system.community.dao.sq.UserWordsV2Dao;
import net.xplife.system.community.dto.sq.*;
import net.xplife.system.community.entity.sq.UserWordsV2;
import net.xplife.system.community.entity.sq.WordsLibraryV2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.JsonKit;
import net.xplife.system.community.vo.sq.PartVo;
import net.xplife.system.community.vo.sq.SymbolVo;
import cn.hutool.crypto.SecureUtil;
@Service
public class UserWordsV2BuService {
    @Autowired
    private UserWordsV2Dao          userWordsDao;
    @Autowired
    private UserWordsV2CacheService userWordsCacheService;
    @Autowired
    private WordsLibraryV2BuService wordsLibraryBuService;

    public void save(UserWordsV2 userWords) {
        userWordsDao.saveEntity(userWords);
        userWordsCacheService.saveUserWords(userWords);
        userWordsCacheService.addUserWordsIdToList(userWords.getUserId(), userWords.getId(), userWords.getCreatetime().getTime());
    }

    /**
     * 获取用户生词数量
     *
     * @param userId
     *            用户ID
     * @return
     */
    public long count(String userId) {
        return userWordsDao.count(userId);
    }

    /**
     * 根据ID获取生词本对象
     *
     * @param id
     *            ID
     * @return 生词本对象
     * @throws ServiceException
     */
    public UserWordsV2 getUserWordsById(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("生词本ID不能为空");
        }
        UserWordsV2 userWords = userWordsCacheService.getUserWords(id);
        if (ToolsKit.isEmpty(userWords)) {
            try {
                userWords = userWordsDao.getById(id, ToolsConst.DATA_SUCCESS_STATUS);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(userWords)) {
                userWordsCacheService.saveUserWords(userWords);
            }
        }
        return userWords;
    }

    /**
     * 根据标识获取生词本对象
     *
     * @param userId
     *            用户ID
     * @param flag
     *            标识
     * @return 生词本对象
     * @throws ServiceException
     */
    public UserWordsV2 getUserWordsByFlag(String userId, String flag) throws ServiceException {
        if (ToolsKit.isEmpty(flag)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("生词本标识不能为空");
        }
        UserWordsV2 userWords = userWordsCacheService.getUserWordsByFlag(userId, flag);
        if (ToolsKit.isEmpty(userWords)) {
            try {
                userWords = userWordsDao.getUserWordsByFlag(userId, flag);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(userWords)) {
                userWordsCacheService.saveUserWords(userWords);
            }
        }
        return userWords;
    }

    /**
     * 初始化用户生词本列表
     *
     * @param userId
     *            用户ID
     * @return
     */
    public List<UserWordsV2> initUserWordsList(String userId) {
        int count = (int) userWordsDao.count(userId);
        if (count > 0) {
            List<UserWordsV2> userWordsList = userWordsDao.findUserWordsList(userId, 0, count, null);
            if (ToolsKit.isNotEmpty(userWordsList)) {
                ToolsKit.Thread.execute(new Runnable() {
                    @Override
                    public void run() {
                        for (UserWordsV2 userWords : userWordsList) {
                            userWordsCacheService.addUserWordsIdToList(userWords.getUserId(), userWords.getId(), userWords.getCreatetime().getTime());
                        }
                    }
                });
            }
            return userWordsList;
        } else {
            return new ArrayList<UserWordsV2>();
        }
    }

    /**
     * 获取生词本ID列表
     *
     * @param userId
     *            用户ID
     * @param page
     *            当前页码
     * @param pageSize
     *            每页大小
     * @param lastId
     *            最后一条数据ID
     * @return 生词本ID列表
     */
    private List<String> findUserWordsIdsList(String userId, int page, int pageSize, String lastId) {
        List<String> idList = new ArrayList<String>();
        if (!userWordsCacheService.existsList(userId)) {
            List<UserWordsV2> userWordsList = this.initUserWordsList(userId);
            if (ToolsKit.isNotEmpty(userWordsList)) {
                boolean isOver = userWordsList.size() < (page * pageSize + pageSize);
                int end = page * pageSize + pageSize;
                if (isOver) {
                    end = userWordsList.size();
                }
                userWordsList = userWordsList.subList(page * pageSize, end);
                for (UserWordsV2 userWords : userWordsList) {
                    idList.add(userWords.getId());
                }
            }
        } else {
            Long rank = null;
            if (ToolsKit.isNotEmpty(lastId)) {
                rank = userWordsCacheService.zrevrank(userId, lastId);
            }
            int start = 0;
            int end = 0;
            if (ToolsKit.isNotEmpty(rank)) {
                start = Integer.parseInt(String.valueOf(rank)) + 1;
                end = start + pageSize;
            } else {
                start = page * pageSize;
                end = page * pageSize + pageSize;
            }
            List<String> idsList = userWordsCacheService.getUserWordsIdsList(userId, start, end);
            if (ToolsKit.isEmpty(idsList)) {
                List<UserWordsV2> userWordsList = userWordsDao.findUserWordsList(userId, page, pageSize, lastId);
                if (ToolsKit.isNotEmpty(userWordsList)) {
                    for (UserWordsV2 userWords : userWordsList) {
                        userWordsCacheService.addUserWordsIdToList(userId, userWords.getId(), userWords.getCreatetime().getTime());
                        idList.add(userWords.getId());
                    }
                }
            } else {
                for (String id : idsList) {
                    idList.add(id);
                }
            }
        }
        return idList;
    }

    /**
     * 获取生词本列表
     *
     * @param userId
     *            用户ID
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @param lastId
     *            最后一条数据ID
     * @return
     * @throws ServiceException
     */
    public List<UserWordsDtoV2> findUserWordsList(String userId, int pageNo, int pageSize, String lastId) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        List<UserWordsDtoV2> userWordsDtoList = new ArrayList<UserWordsDtoV2>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            List<String> idList = this.findUserWordsIdsList(userId, pageNo, pageSize, lastId);
            if (ToolsKit.isNotEmpty(idList)) {
                for (String id : idList) {
                    UserWordsV2 userWords = this.getUserWordsById(id);
                    if (ToolsKit.isNotEmpty(userWords)) {
                        WordsLibraryV2 wordsLibrary = wordsLibraryBuService.getWordsLibraryByFlag(userWords.getFlag());
                        if (ToolsKit.isNotEmpty(wordsLibrary)) {
                            UserWordsDtoV2 userWordsDto = new UserWordsDtoV2();
                            userWordsDto.setId(userWords.getId());
                            userWordsDto.setName(userWords.getName());
                            userWordsDto.setPhonetic(wordsLibrary.getPhonetic());
                            userWordsDto.setDefinitions(wordsLibrary.getDefinitions());
                            userWordsDto.setExamples(wordsLibrary.getExamples());
                            userWordsDto.setPronunciation(wordsLibrary.getPronunciation());
                            userWordsDto.setCreateDate(userWords.getCreatetime());
                            userWordsDtoList.add(userWordsDto);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return userWordsDtoList;
    }

    /**
     * 删除生词本信息
     *
     * @param id
     *            ID
     * @throws ServiceException
     */
    public void delUserWords(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录ID不能为空");
        }
        List<String> idList = JsonKit.jsonParseArray(id, String.class);
        if (ToolsKit.isNotEmpty(idList)) {
            for (String tmpId : idList) {
                UserWordsV2 userWords = this.getUserWordsById(tmpId);
                if (ToolsKit.isNotEmpty(userWords)) {
                    userWords.setStatus(ToolsConst.DATA_DELETE_STATUS);
                    userWordsDao.saveEntity(userWords);
                    userWordsCacheService.delUserWords(tmpId);
                    userWordsCacheService.delUserWordsByFlag(userWords.getUserId(), userWords.getFlag());
                    userWordsCacheService.removeUserWordsIdToList(userWords.getUserId(), tmpId);
                }
            }
        }
    }

    /**
     * 保存生词本
     *
     * @param userId
     * @param addUserWordsDto
     * @throws ServiceException
     */
    public Map<String, String> saveUserWords(String userId, AddUserWordsDtoV2 addUserWordsDto) throws ServiceException {
        Map<String, String> map = new HashMap<String, String>();
        try {
            if (ToolsKit.isEmpty(addUserWordsDto.getName())) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("单词不能为空");
            }
            if (ToolsKit.isEmpty(addUserWordsDto.getPhonetic())) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("音标不能为空");
            }
            if (ToolsKit.isEmpty(addUserWordsDto.getDefinitions())) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("释义不能为空");
            }
            String flag = SecureUtil.md5(addUserWordsDto.getName().trim());
            UserWordsV2 userWords = this.getUserWordsByFlag(userId, flag);
            if (ToolsKit.isNotEmpty(userWords)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("已添加过该单词");
            }
            WordsLibraryV2 wordsLibrary = wordsLibraryBuService.getWordsLibraryByFlag(flag);
            if (ToolsKit.isEmpty(wordsLibrary)) {
                wordsLibrary = new WordsLibraryV2();
                ToolsKit.setIdEntityData(wordsLibrary, userId);
            }
            wordsLibrary.setName(addUserWordsDto.getName().trim());
            wordsLibrary.setFlag(flag);
            wordsLibrary.setPhonetic(addUserWordsDto.getPhonetic());
            wordsLibrary.setDefinitions(addUserWordsDto.getDefinitions());
            wordsLibrary.setExamples(addUserWordsDto.getExamples());
            wordsLibrary.setPronunciation(addUserWordsDto.getPronunciation());
            wordsLibraryBuService.save(wordsLibrary);
            userWords = new UserWordsV2();
            ToolsKit.setIdEntityData(userWords, userId);
            userWords.setWordsId(wordsLibrary.getId());
            userWords.setName(addUserWordsDto.getName());
            userWords.setFlag(flag);
            userWords.setUserId(userId);
            this.save(userWords);
            map.put("id", userWords.getId());
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("添加生词失败");
        }
        return map;
    }
}