package net.xplife.system.shitiku.service;

import com.alibaba.fastjson.JSONArray;
import net.xplife.system.account.dto.user.UserLoginDto;
import net.xplife.system.account.interfaces.user.UserAccountService;
import net.xplife.system.shitiku.dto.*;
import net.xplife.system.shitiku.entity.IndexFunction;
import net.xplife.system.shitiku.entity.SelectedQuestion;
import net.xplife.system.shitiku.enums.IndexFunctionEnums;
import net.xplife.system.shitiku.enums.SelectedQuestionTypeEnums300;
import net.xplife.system.shitiku.jvm.JvmCacheBuService;
import net.xplife.system.shitiku.utils.Constant;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.JsonKit;
import net.xplife.system.web.config.CommonProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class SelectedQuestionBuService {
    @Autowired
    private JyeooBuService jyeooBuService;
    @Autowired
    private IndexFunctionBuService  indexFunctionBuService;
    @Autowired
    private UserAccountService      userAccountService;
    @Autowired
    private CommonProperties        commonProperties;
    @Autowired
    private JvmCacheBuService jvmCacheBuService;
    @Autowired
    private SelectQuestionBuService selectQuestionBuService;

    /**
     * 获取学科信息
     * 
     * @param userId
     *            用户ID
     * @return
     * @throws ServiceException
     */
    public SubjectListDto getSubjects(String userId, String version) throws ServiceException {
//        if (ToolsKit.isEmpty(version)){
//            throw new ServiceException("version不能为空");
//        }
        IndexFunction indexFunction = indexFunctionBuService.getDefaultIndexFunction(userId, IndexFunctionEnums.JXTK.getVersion());
        if (ToolsKit.isEmpty(indexFunction.getJyeooId())) {
            UserLoginDto userLoginDto = userAccountService.getUserInfo(userId);
            if (ToolsKit.isNotEmpty(userLoginDto) && ToolsKit.isNotEmpty(userLoginDto.getUserInfoDto())) {
                String sex = ToolsConst.DEFAULT_M_SEX.equals(userLoginDto.getUserInfoDto().getSex()) ? "1" : "2";
                String jyeooId = jyeooBuService.register("珠海市星星机学院", userId, Constant.JYEOO_DEFAULT_PWD, userLoginDto.getUserInfoDto().getNickName(), "2", sex);
                if (ToolsKit.isNotEmpty(jyeooId)) {
                    indexFunction.setJyeooId(jyeooId);
                    indexFunctionBuService.save(indexFunction);
                }
            }
        }
        String result = jyeooBuService.getSubjects(userId);
        JSONArray JSONArray = JsonKit.jsonParseArray(result);
        SubjectListDto subjectListDto = new SubjectListDto();
        subjectListDto.setXx(new ArrayList<SubjectsDto>());
        subjectListDto.setCz(new ArrayList<SubjectsDto>());
        subjectListDto.setGz(new ArrayList<SubjectsDto>());
        for (int i = 0; i < JSONArray.size(); i++) {
            String value = JSONArray.getString(i);
            JSONArray array = JSONArray.getJSONArray(i);
            SubjectsDto subjectsDto = new SubjectsDto();
            if (("道德与法治").equals(array.getString(2))) {
                subjectsDto.setName("政治");
            } else {
                subjectsDto.setName(array.getString(2).replace("小学", "").replace("初中", "").replace("高中", ""));
            }
            subjectsDto.setSubject(array.getString(1));

            for (SelectedQuestionTypeEnums300 selectedQuestionTypeEnums : SelectedQuestionTypeEnums300.values()) {
                if (selectedQuestionTypeEnums.getValue().equals(subjectsDto.getName())) {
                    subjectsDto.setEnglish(selectedQuestionTypeEnums.getEnglish());
                    subjectsDto.setSort(selectedQuestionTypeEnums.getType());
                    subjectsDto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain() + commonProperties.getH5Env(), selectedQuestionTypeEnums.getIcon()));
                    break;
                } else if (subjectsDto.getName().equals("道德与法治") || subjectsDto.getName().equals("道法")) {
                    SelectedQuestionTypeEnums300 typeEnums = SelectedQuestionTypeEnums300.DF;
                    subjectsDto.setEnglish(typeEnums.getEnglish());
                    subjectsDto.setSort(typeEnums.getType());
                    subjectsDto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain() + commonProperties.getH5Env(), typeEnums.getIcon()));
                    break;
                }
            }

            if (value.contains("小学")) {
                subjectListDto.getXx().add(subjectsDto);
            } else if (value.contains("初中") || value.contains("道德与法治")) {
                subjectListDto.getCz().add(subjectsDto);
            } else if (value.contains("高中")) {
                subjectListDto.getGz().add(subjectsDto);
            }
        }
        subjectListDto.getXx().sort((item1, item2) -> item1.getSort() - item2.getSort());
        subjectListDto.getCz().sort((item1, item2) -> item1.getSort() - item2.getSort());
        subjectListDto.getGz().sort((item1, item2) -> item1.getSort() - item2.getSort());
        return subjectListDto;
    }

    /**
     * 获取所选学科的教材（包含教材章节）
     * 
     * @param userId
     *            用户ID
     * @param subject
     *            学科
     * @return
     * @throws ServiceException
     */
    public Book getBooks(String userId, String subject, String editionName, String name) throws ServiceException {
        if (ToolsKit.isEmpty(subject)) {
            throw new ServiceException("学科不能为空", ExceptionEnums.PARAM_ERROR.getCode());
        }
        String value = jyeooBuService.getBooks(userId, subject);

        List<Book> books = JsonKit.jsonParseArray(value, Book.class);
//        List<Book> booksTemp = JsonKit.jsonParseArray(value, Book.class);
//        List<Book> books = new ArrayList<>();
//        for (Book book : booksTemp) {
//            if (ToolsKit.isNotEmpty(book.getChildren())){
//                books.add(book);
//            }
//        }
        if (books != null && books.size() > 0) {
            if (ToolsKit.isNotEmpty(editionName) && ToolsKit.isNotEmpty(name)) {
                for (Book book : books) {
                    if (book.getEditionName().equals(editionName) && book.getName().equals(name)) {
                        List<Category> children = gettingCategoryList(book.getChildren());
                        book.setChildren(children);
                        return book;
                    }
                }
            }
            List<Category> children = gettingCategoryList(books.get(0).getChildren());
            books.get(0).setChildren(children);
            return books.get(0);
        } else {
            throw new ServiceException("此学科暂无数据", ExceptionEnums.PARAM_ERROR.getCode());
        }
    }

    /**
     * 获取所选学科的教材（包含教材章节）
     * 
     * @param userId
     *            用户ID
     * @param subject
     *            学科
     * @return
     * @throws ServiceException
     */
    public List<BookListDto> getBookList(String userId, String subject) throws ServiceException {
        if (ToolsKit.isEmpty(subject)) {
            throw new ServiceException("学科不能为空", ExceptionEnums.PARAM_ERROR.getCode());
        }
        String value = jyeooBuService.getBooks(userId, subject);
        List<Book> books = JsonKit.jsonParseArray(value, Book.class);


        List<BookListDto> dtoList = new ArrayList<BookListDto>();

        List<Map<String, Object>> bookList = (List<Map<String, Object>>) this.getEditions(userId, subject);

        for (Map<String, Object> obj: bookList) {

            BookListDto dto = new BookListDto();
            dto.setName(obj.get("Value").toString());
            dto.setChildren(new ArrayList<>());
            dtoList.add(dto);
        }

        for (BookListDto bookListDto:dtoList ) {
            for (Book book : books) {
                if (book.getEditionName().equals(bookListDto.getName())) {
                    bookListDto.getChildren().add(new BookListDto(book.getName()));
                }
            }
        }

        // 过滤掉所有不存在子节点的数据
        dtoList.removeIf(item -> {
            return item.getChildren().size()==0;
        });


//        BookListDto bookListDto = null;
//        String editionName = null;
//        for (Book book : books) {
//            if (ToolsKit.isEmpty(editionName) || !book.getEditionName().equals(editionName)) {
//                bookListDto = new BookListDto();
//                bookListDto.setName(book.getEditionName());
//                bookListDto.setChildren(new ArrayList<BookListDto>());
//                dtoList.add(bookListDto);
//                editionName = book.getEditionName();
//            }
//            bookListDto.getChildren().add(new BookListDto(book.getName()));
//        }
        return dtoList;
    }

    /**
     * 获取筛选参数
     * 
     * @param userId
     * @param subject
     * @return
     * @throws ServiceException
     */
    public ScreenParamDto getScreen(String userId, String subject) throws ServiceException {
        if (ToolsKit.isEmpty(subject)) {
            throw new ServiceException("学科不能为空", ExceptionEnums.PARAM_ERROR.getCode());
        }
        ScreenParamDto screenParamDto = new ScreenParamDto();
        screenParamDto.setCate(new ArrayList<KeyValuePair<String, String>>());
        screenParamDto.setQuesCate(new ArrayList<KeyValuePair<Object, String>>());
        screenParamDto.setDegree(new ArrayList<KeyValuePair<String, String>>());
        screenParamDto.setSource(new ArrayList<KeyValuePair<String, String>>());
        screenParamDto.setYear(new ArrayList<KeyValuePair<String, String>>());
        screenParamDto.setPd(new ArrayList<KeyValuePair<String, String>>());
        screenParamDto.setPo(new ArrayList<KeyValuePair<String, String>>());
        screenParamDto.setArea(new ArrayList<AreaDto>());
        screenParamDto.getCate().add(new KeyValuePair<String, String>("0", "全部"));
        screenParamDto.getSource().add(new KeyValuePair<String, String>("0", "全部"));
        screenParamDto.getQuesCate().add(new KeyValuePair<Object, String>("", "全部"));
        screenParamDto.getQuesCate().add(new KeyValuePair<Object, String>("sc", "真题集"));
        screenParamDto.getQuesCate().add(new KeyValuePair<Object, String>("gc", "好题集"));
        screenParamDto.getQuesCate().add(new KeyValuePair<Object, String>("rc", "常考题"));
        screenParamDto.getQuesCate().add(new KeyValuePair<Object, String>("yc", "压轴题"));
        screenParamDto.getQuesCate().add(new KeyValuePair<Object, String>("ec", "易错题"));
        screenParamDto.getQuesCate().add(new KeyValuePair<Object, String>("er", "用户错题"));
        screenParamDto.getDegree().add(new KeyValuePair<String, String>("0", "全部"));
        screenParamDto.getDegree().add(new KeyValuePair<String, String>("11", "易"));
        screenParamDto.getDegree().add(new KeyValuePair<String, String>("12", "较易"));
        screenParamDto.getDegree().add(new KeyValuePair<String, String>("13", "中等"));
        screenParamDto.getDegree().add(new KeyValuePair<String, String>("14", "较难"));
        screenParamDto.getDegree().add(new KeyValuePair<String, String>("15", "难"));
        screenParamDto.getPo().add(new KeyValuePair<String, String>("0", "综合排序"));
        screenParamDto.getPo().add(new KeyValuePair<String, String>("1", "组卷次数"));
        screenParamDto.getPo().add(new KeyValuePair<String, String>("2", "真题次数"));
        screenParamDto.getPo().add(new KeyValuePair<String, String>("3", "试题难度"));
        screenParamDto.getPd().add(new KeyValuePair<String, String>("1", "降序"));
        screenParamDto.getPd().add(new KeyValuePair<String, String>("0", "升序"));
        screenParamDto.getYear().add(new KeyValuePair<String, String>("0", "全部"));

        int indexYear = ToolsKit.Date.thisYear();
        for (int i = 0; i < 5; i++) {
            String year = String.valueOf(indexYear-i);
            screenParamDto.getYear().add(new KeyValuePair<String, String>(year, year));
        }
        StringBuffer yearStr = new StringBuffer("");
        for (int i = 5; i < 10; i++) {
            String year = String.valueOf(indexYear-i);
            if (i!=9){
                yearStr.append(year + ",");
            } else {
                yearStr.append(year);
            }
        }
        screenParamDto.getYear().add(new KeyValuePair<String, String>(yearStr.toString(), "更早"));

//        screenParamDto.getYear().add(new KeyValuePair<String, String>("2023", "2023"));
//        screenParamDto.getYear().add(new KeyValuePair<String, String>("2022", "2022"));
//        screenParamDto.getYear().add(new KeyValuePair<String, String>("2021", "2021"));
//        screenParamDto.getYear().add(new KeyValuePair<String, String>("2020", "2020"));
//        screenParamDto.getYear().add(new KeyValuePair<String, String>("2019", "2019"));
//        screenParamDto.getYear().add(new KeyValuePair<String, String>("2018,2017,2016,2015,2014", "更早"));

        String cates = jyeooBuService.getCates(userId, subject);
        if (cates != null) {
            List<KeyValuePair> list = JsonKit.jsonParseArray(cates, KeyValuePair.class);
            if (list != null) {
                list.forEach(item -> {
                    if (ToolsKit.isNotEmpty(item.getKey()) && ToolsKit.isNotEmpty(item.getValue())) {
                        screenParamDto.getCate().add(new KeyValuePair<String, String>(item.getKey().toString(), item.getValue().toString()));
                    }
                });
            }
        }

        String source = jyeooBuService.getSources(userId, subject);
        if (source != null) {
            List<KeyValuePair> list2 = JsonKit.jsonParseArray(source, KeyValuePair.class);
            if (list2 != null) {
                list2.forEach(item -> {
                    if (ToolsKit.isNotEmpty(item.getKey()) && ToolsKit.isNotEmpty(item.getValue())) {
                        screenParamDto.getSource().add(new KeyValuePair<String, String>(item.getKey().toString(), item.getValue().toString()));
                    }
                });
            }
        }

        String area = jyeooBuService.getArea(userId);
        if (area != null) {
            List<Area> list2 = JsonKit.jsonParseArray(area, Area.class);
            List<AreaDto> result = new ArrayList<>();
            AreaDto all = new AreaDto();
            all.setKey("");
            all.setValue("不限");
            result.add(all);
            if (list2 != null) {
                list2.forEach(item -> {
                    if (-1 == item.getType() || 0 == item.getType()){
                        AreaDto areaTemp = new AreaDto();
                        areaTemp.setKey(item.getId());
                        areaTemp.setValue(item.getName());
                        if ("全国".equals(item.getName())){
                            areaTemp.setValue("全国卷");
                        }
                        result.add(areaTemp);
                    }
                });
//                result.forEach(item -> {
//                    setAreaChildren(item, list2);
//                });
                screenParamDto.setArea(result);
            }
        }
        return screenParamDto;
    }

    private void setAreaChildren(AreaDto areaDto, List<Area> areaList){
        areaDto.setChildren(new ArrayList<>());
        if ("1000000".equals(areaDto.getKey())){
            // 全国这个值，特殊处理
            return;
        }
        for (Area area: areaList) {
            if (areaDto.getKey().equals(area.getPid())){
                AreaDto areaTemp = new AreaDto();
                areaTemp.setKey(area.getId());
                areaTemp.setValue(area.getName());
                areaDto.getChildren().add(areaTemp);
                setAreaChildren(areaTemp, areaList);
            }
        }
    }

    /**
     * 获取教材版本
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @return
     * @throws ServiceException
     */
    public Object getEditions(String userId, String subject) throws ServiceException {
        if (ToolsKit.isEmpty(subject)) {
            throw new ServiceException("学科不能为空", ExceptionEnums.PARAM_ERROR.getCode());
        }
        String value = jyeooBuService.getEditions(userId, subject);
        return JsonKit.jsonParseArray(value);
    }

    /**
     * 获取学科考点
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @return
     * @throws ServiceException
     */
    public List<Point> getPoints(String userId, String subject) throws ServiceException {
        if (ToolsKit.isEmpty(subject)) {
            throw new ServiceException("学科不能为空", ExceptionEnums.PARAM_ERROR.getCode());
        }
        String value = jyeooBuService.getPoints(userId, subject);
        return JsonKit.jsonParseArray(value, Point.class);
    }

    /**
     * 获取试题解析
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @param id
     *            id
     * @return
     * @throws ServiceException
     */
    public Ques getQues(String userId, String subject, String id) throws ServiceException {
        if (ToolsKit.isEmpty(subject)) {
            throw new ServiceException("学科不能为空", ExceptionEnums.PARAM_ERROR.getCode());
        }
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException("id不能为空", ExceptionEnums.PARAM_ERROR.getCode());
        }
        String value = jyeooBuService.getQues(userId, subject, id);
        if ("[]".equals(value)){
            throw new ServiceException("查询不到该题的解析", ExceptionEnums.PARAM_ERROR.getCode());
        }
        System.out.println(value);
        List<Ques> quesList = JsonKit.jsonParseArray(value, Ques.class);
        if (ToolsKit.isEmpty(quesList)){
            throw new ServiceException("查询不到该题的解析", ExceptionEnums.PARAM_ERROR.getCode());
        }
//        Ques ques = JsonKit.jsonParseObject(value, Ques.class);
        Ques ques = quesList.get(0);
        ques.setTemplate(jvmCacheBuService.getTemplateContent());

//        SelectedQuestion selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getID());
//        if (ToolsKit.isNotEmpty(selectedQuestion)) {
//            ques.setCollectId(selectedQuestion.getId());
//        }
        SelectedQuestion selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getSID());
        if (ToolsKit.isNotEmpty(selectedQuestion)) {
            ques.setCollectId(selectedQuestion.getId());
        } else {
            // 兼容老数据
            selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getID());
            if (ToolsKit.isNotEmpty(selectedQuestion)) {
                ques.setCollectId(selectedQuestion.getId());
            }
        }
        if (ques.getContent() != null) {
            // 禁用编辑框控件
            ques.setContent(ques.getContent().replaceAll("contenteditable=\"true\"", ""));
            ques.setContent(ques.getContent().replaceAll("contenteditable='true'", ""));
        }
        System.out.println("------------------------------------------");
        System.out.println(ques.getAnswers());
        System.out.println(ques.getAnalyse());
        System.out.println(ques.getContent());
        System.out.println(ques.getTemplate());
        System.out.println("------------------------------------------");
        return ques;
    }

    /**
     * 获取相似试题
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @param id
     *            id
     * @return
     * @throws ServiceException
     */
    public List<Ques> getRelativeQueses(String userId, String subject, String id) throws ServiceException {
        if (ToolsKit.isEmpty(subject)) {
            throw new ServiceException("学科不能为空", ExceptionEnums.PARAM_ERROR.getCode());
        }
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException("id不能为空", ExceptionEnums.PARAM_ERROR.getCode());
        }
        String value = jyeooBuService.getRelativeQueses(userId, subject, id, ToolsConst.PHONEPAGESIZE);
        List<Ques> quesList = JsonKit.jsonParseArray(value, Ques.class);
        if (ToolsKit.isNotEmpty(quesList)) {
            for (int i = 0; i < quesList.size(); i++) {
                Ques ques = quesList.get(i);
                ques.setTemplate(jvmCacheBuService.getTemplateContent());

                SelectedQuestion selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getSID());
                if (ToolsKit.isNotEmpty(selectedQuestion)) {
                    ques.setCollectId(selectedQuestion.getId());
                } else {
                    selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getID());
                    if (ToolsKit.isNotEmpty(selectedQuestion)) {
                        ques.setCollectId(selectedQuestion.getId());
                    }
                }

                if (ToolsKit.isNotEmpty(ques.getSID())){
                    ques.setID(ques.getSID());
                }

                quesList.set(i, ques);
            }
        }
        return quesList;
    }

    /**
     * 按章节或考点获取试题列表
     * 
     * @param userId
     * @param searchQuesParamDto
     * @return
     * @throws ServiceException
     */
    public SearchObject searchQues(String userId, SearchQuesParamDto searchQuesParamDto) throws ServiceException {
        int pageSize = ToolsConst.PHONEPAGESIZE;
        // 第3方接口已限制最大 10 条
        pageSize = 10;
        int pageNo = 2 * searchQuesParamDto.getPi() - 1;
        if (pageNo <= 0) {
            pageNo = 1;
        }

        if (searchQuesParamDto.getPi() > 10) {
            // 因为第三方数据平台只返回最多200条，超过10页则返回空
            SearchObject searchObj = new SearchObject();
            searchObj.setTemplate(jvmCacheBuService.getTemplateContent());
            searchObj.setPageSize(ToolsConst.PHONEPAGESIZE);
            searchObj.setPageIndex(pageNo);
            searchObj.setCount(200);
            searchObj.setKeys(new ArrayList<>());
            searchObj.setData(new ArrayList<>());
            return searchObj;
        }
        System.out.println();
        SearchObject searchObject = jyeooBuService.searchQues(userId, searchQuesParamDto.getSubject(), searchQuesParamDto.getTp(), searchQuesParamDto.getP1(),
                searchQuesParamDto.getP2(), searchQuesParamDto.getP3(), searchQuesParamDto.getCate(), searchQuesParamDto.getDegree(),
                searchQuesParamDto.getSc(), searchQuesParamDto.getGc(), searchQuesParamDto.getRc(), searchQuesParamDto.getYc(), searchQuesParamDto.getEc(),
                searchQuesParamDto.getEr(), searchQuesParamDto.getRg(), searchQuesParamDto.getSource(), searchQuesParamDto.getYear(), searchQuesParamDto.getPo(),
                searchQuesParamDto.getPd(), pageNo, pageSize, ToolsConst.STATUS_0);
        searchObject.setTemplate(jvmCacheBuService.getTemplateContent());
        if (ToolsKit.isNotEmpty(searchObject.getData())) {
            for (int i = 0; i < searchObject.getData().size(); i++) {
                Ques ques = searchObject.getData().get(i);
                ques.setTemplate(jvmCacheBuService.getTemplateContent());

                SelectedQuestion selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getSID());
                if (ToolsKit.isNotEmpty(selectedQuestion)) {
                    ques.setCollectId(selectedQuestion.getId());
                } else {
                    selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getID());
                    if (ToolsKit.isNotEmpty(selectedQuestion)) {
                        ques.setCollectId(selectedQuestion.getId());
                    }
                }
                // 第三方接口变更，试题的sid替代id
                if (ToolsKit.isNotEmpty(ques.getSID())){
                    ques.setID(ques.getSID());
                }

                searchObject.getData().set(i, ques);
            }

            if (searchObject.getData().size() == pageSize) {
                // 获取第2页数据
                pageNo = pageNo + 1;
                SearchObject searchObjectNext = jyeooBuService.searchQues(userId, searchQuesParamDto.getSubject(), searchQuesParamDto.getTp(), searchQuesParamDto.getP1(),
                        searchQuesParamDto.getP2(), searchQuesParamDto.getP3(), searchQuesParamDto.getCate(), searchQuesParamDto.getDegree(),
                        searchQuesParamDto.getSc(), searchQuesParamDto.getGc(), searchQuesParamDto.getRc(), searchQuesParamDto.getYc(), searchQuesParamDto.getEc(),
                        searchQuesParamDto.getEr(), searchQuesParamDto.getRg(), searchQuesParamDto.getSource(), searchQuesParamDto.getYear(), searchQuesParamDto.getPo(),
                        searchQuesParamDto.getPd(), pageNo, pageSize, ToolsConst.STATUS_0);

                if (ToolsKit.isNotEmpty(searchObjectNext.getData())) {
                    for (int i = 0; i < searchObjectNext.getData().size(); i++) {
                        Ques ques = searchObjectNext.getData().get(i);
                        ques.setTemplate(jvmCacheBuService.getTemplateContent());
                        SelectedQuestion selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getSID());
                        if (ToolsKit.isNotEmpty(selectedQuestion)) {
                            ques.setCollectId(selectedQuestion.getId());
                        } else {
                            selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getID());
                            if (ToolsKit.isNotEmpty(selectedQuestion)) {
                                ques.setCollectId(selectedQuestion.getId());
                            }
                        }
                        // 第三方接口变更，试题的sid替代id
                        if (ToolsKit.isNotEmpty(ques.getSID())){
                            ques.setID(ques.getSID());
                        }

                        searchObject.getData().add(ques);
                    }
                }
            }
        }
        searchObject.setPageSize(ToolsConst.PHONEPAGESIZE);
        // System.out.println(JsonKit.toJsonString(searchObject));
        return searchObject;
    }

    public SearchObject keySearch(String userId, SearchQuesParamDto searchQuesParamDto) {
        // 关键词搜索，第三方确认，只会返回10条记录，不会再多
        int pageSize = ToolsConst.PHONEPAGESIZE;
        // 第3方接口已限制最大 10 条
        pageSize = 10;
        int pageNo = searchQuesParamDto.getPi();
        if (pageNo <= 0) {
            pageNo = 1;
        }

        if (searchQuesParamDto.getPi() > 1) {
            // 关键词搜索，第三方确认，只会返回10条记录，不会再多
            SearchObject searchObj = new SearchObject();
            searchObj.setTemplate(jvmCacheBuService.getTemplateContent());
            searchObj.setPageSize(ToolsConst.PHONEPAGESIZE);
            searchObj.setPageIndex(pageNo);
            searchObj.setCount(10);
            searchObj.setKeys(new ArrayList<>());
            searchObj.setData(new ArrayList<>());
            return searchObj;
        }
        SearchObject searchObject = jyeooBuService.keySearch(userId, searchQuesParamDto.getKeySearch(), searchQuesParamDto.getSubject(),  searchQuesParamDto.getTp(), pageNo, pageSize);
        searchObject.setTemplate(jvmCacheBuService.getTemplateContent());
        if (ToolsKit.isNotEmpty(searchObject.getData())) {
            for (int i = 0; i < searchObject.getData().size(); i++) {
                Ques ques = searchObject.getData().get(i);
                ques.setTemplate(jvmCacheBuService.getTemplateContent());
                SelectedQuestion selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getSID());
                if (ToolsKit.isNotEmpty(selectedQuestion)) {
                    ques.setCollectId(selectedQuestion.getId());
                } else {
                    selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getID());
                    if (ToolsKit.isNotEmpty(selectedQuestion)) {
                        ques.setCollectId(selectedQuestion.getId());
                    }
                }
                // 第三方接口变更，试题的sid替代id
                if (ToolsKit.isNotEmpty(ques.getSID())){
                    ques.setID(ques.getSID());
                }
                searchObject.getData().set(i, ques);
            }

//            if (searchObject.getData().size() == pageSize) {
//                // 获取第2页数据
//                pageNo = pageNo + 1;
//                SearchObject searchObjectNext = jyeooBuService.keySearch(userId, searchQuesParamDto.getKeySearch(), searchQuesParamDto.getSubject(), searchQuesParamDto.getTp(), pageNo, pageSize);
//
//                if (ToolsKit.isNotEmpty(searchObjectNext.getData())) {
//                    for (int i = 0; i < searchObjectNext.getData().size(); i++) {
//                        Ques ques = searchObjectNext.getData().get(i);
//                        ques.setTemplate(jvmCacheBuService.getTemplateContent());
//                        SelectedQuestion selectedQuestion = selectQuestionBuService.getByJyeooId(userId, ques.getID());
//                        if (ToolsKit.isNotEmpty(selectedQuestion)) {
//                            ques.setCollectId(selectedQuestion.getId());
//                        }
//                        searchObject.getData().add(ques);
//                    }
//                }
//            }
        }
        searchObject.setPageSize(ToolsConst.PHONEPAGESIZE);
        return searchObject;
    }

    private List<Category> gettingCategoryList(List<Category> categories){
        List<Category> categoryList = new ArrayList<>();
        for (Category category: categories) {
            if (ToolsKit.isNotEmpty(category.getChildren())){
                // System.out.println("category.getChildren()不为空，进入下循环"+category.getID());
                // 如果当前下面还有节点，则地柜递归
                List<Category> children = gettingCategoryList(category.getChildren());
                category.setChildren(children);
                categoryList.add(category);
            } else if (ToolsKit.isNotEmpty(category.getQuesCount())){
                // 不为空的话，说明当前节点下有考题，组装下返回
                categoryList.add(gettingCategory(category));
                // System.out.println("category=" + JSON.toJSONString(categoryList.get(categoryList.size()-1)));
            }
        }
        return categoryList;
    }

    private Category gettingCategory(Category category){
        // 进来前已判断是否有题目了，如果有
        if (ToolsKit.isEmpty(category.getPoints())){
            return category;
        } else {
            List<Point> newPointList = new ArrayList<>();
            for (Point point: category.getPoints()) {
                if (ToolsKit.isNotEmpty(point.getQuesCount())){
                    newPointList.add(gettingPoint(point));
                }
            }
            category.setPoints(newPointList);
            return category;
        }
    }

    private Point gettingPoint(Point point) {
        // 进来前已判断是否有题目了，如果有
        if (ToolsKit.isEmpty(point.getChildren())){
            // 如果没有下节点，则返回当前
            return point;
        } else {
            List<Point> newPointList = new ArrayList<>();
            for (Point pointinner : point.getChildren()) {
                if (ToolsKit.isNotEmpty(gettingPoint(pointinner))){
                    newPointList.add(pointinner);
                }
            }
            point.setChildren(newPointList);
            return point;
        }
    }
}
