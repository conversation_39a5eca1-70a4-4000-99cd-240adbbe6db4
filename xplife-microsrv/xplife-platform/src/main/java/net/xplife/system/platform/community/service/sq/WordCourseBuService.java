package net.xplife.system.platform.community.service.sq;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.xplife.system.community.dto.sq.*;
import net.xplife.system.platform.community.cache.sq.WordsCacheService;
import net.xplife.system.community.entity.sq.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.config.CommonProperties;
import net.xplife.system.platform.community.dao.sq.WordCourseDao;
import net.xplife.system.community.entity.user.UserStatistics;
import net.xplife.system.community.enums.sq.CourseResEnums;
import net.xplife.system.community.enums.sq.WordTypeEnums;
import net.xplife.system.platform.community.service.jvm.JvmCacheBuService;
import net.xplife.system.platform.community.service.user.UserStatisticsBuService;
import net.xplife.system.community.utils.Constant;

@Service
public class WordCourseBuService {
    @Autowired
    private WordCourseDao           wordCourseDao;
    @Autowired
    private JvmCacheBuService       jvmCacheBuService;
    @Autowired
    private CommonProperties        commonProperties;
    @Autowired
    private WordsBuService          wordsBuService;
    @Autowired
    private UserCourseBuService     userCourseBuService;
    @Autowired
    private UserWordsBuService      userWordsBuService;
    @Autowired
    private UserStatisticsBuService userStatisticsBuService;

    @Autowired
    private WordsLibraryBuService   wordsLibraryBuService;
    @Autowired
    private WordsCacheService wordsCacheService;

    /**
     * 获取所有课程列表
     * 
     * @return
     */
    public List<WordCourseV2> findAll() {
        return wordCourseDao.findList(WordTypeV2.SORT_FIELD);
    }

    /**
     * 获取词库本列表
     * 
     * @param userId
     *            用户ID
     * @return
     * @throws ServiceException
     */
    public WordCourseDto findWordCourse(String userId, String version) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        UserCourseV2 userCourse = userCourseBuService.getUserCourseByUserId(userId);
        UserStatistics userStatistics = userStatisticsBuService.getUserStatistics(userId);
        List<String> typeList = jvmCacheBuService.getWordTypeIdList();// 词库根节点ID集合
        WordCourseDto wordCourseDto = new WordCourseDto();
        wordCourseDto.setTypeList(new ArrayList<WordTypeDto>());
        if (ToolsKit.isNotEmpty(userCourse)) {
            wordCourseDto.setCurrCourseId(userCourse.getCourseId());
            if (userCourse.getCurrCount() >= userCourse.getTotalCount()) {
                wordCourseDto.setCourseStatus(ToolsConst.STATUS_1);
            }
        }
//        boolean isNewVer = StringUtils.isNotBlank(version) && Integer.parseInt(version.replace(".", "")) >= Constant.VERSION_230;
        boolean isNewVer = true;
        Map<String, List<CourseInfoDto>> wordCourses = new HashMap<String, List<CourseInfoDto>>();
        try {
            for (String id : typeList) {
                WordTypeV2 wordType = jvmCacheBuService.getWordType(id);// 根节点
                List<String> typeChildList = jvmCacheBuService.getWordTypeChildIdList(id);// 词库子节点ID集合
                WordTypeDto wordTypeDto = new WordTypeDto(wordType.getId(), wordType.getName(), wordType.getType());
                wordTypeDto.setSubTypeList(new ArrayList<WordTypeDto>());
                for (String typeChild : typeChildList) {
                    WordTypeV2 childWordType = jvmCacheBuService.getWordType(typeChild);
                    wordTypeDto.getSubTypeList().add(new WordTypeDto(childWordType.getId(), childWordType.getName(), childWordType.getType()));
                    List<CourseInfoDto> dtoList = null;
                    if (wordCourses.containsKey(childWordType.getId())) {
                        dtoList = wordCourses.get(childWordType.getId());
                    } else {
                        dtoList = new ArrayList<CourseInfoDto>();
                    }
                    List<String> rootIdList = jvmCacheBuService.getCourseRootIdList(childWordType.getId());// 词库子节点下的课程ID集合
                    if (ToolsKit.isNotEmpty(rootIdList)) {
                        for (String wcId : rootIdList) {
                            WordCourseV2 wordCourse = jvmCacheBuService.getWordCourse(wcId);
                            if (ToolsKit.isNotEmpty(wordCourse)) {
                                CourseInfoDto dto = new CourseInfoDto();
                                if (ToolsKit.isNotEmpty(userCourse)) {
                                    dto.setCurrCourseId(userCourse.getCourseId());
                                    if (ToolsKit.isNotEmpty(userStatistics.getCourseIds()) && userStatistics.getCourseIds().contains(wordCourse.getId())) {
                                        dto.setCourseStatus(ToolsConst.STATUS_1);
                                    }
                                }
                                dto.setCourseId(wordCourse.getId());
                                dto.setTypeName(wordCourse.getTypeName());
                                if (("出国").equals(wordCourse.getTypeName()) || ("大学").equals(wordCourse.getTypeName())) {
                                    dto.setName(wordCourse.getTypeName() + wordCourse.getRemark());
                                } else {
                                    dto.setName(wordCourse.getName());
                                }
                                dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(),
                                        wordCourse.getPics().get(CourseResEnums.P200x264.getKey())));
                                dto.setWordCount(wordCourse.getTotalCount());
                                // 2.3版本添加 currCount 和 planCount, Update By RabyGao on 2019-12-10
                                if (isNewVer) {
                                    UserCourseV2 currUserCourse = userCourseBuService.getUserCourseDeletedByUserId(userId, dto.getCourseId());
                                    if (userCourse != null && userCourse.getCourseId() != null && dto.getCourseId().equals(userCourse.getCourseId())) {
                                        dto.setCurrCount(userCourse.getCurrCount());
                                        dto.setPlanCount(userCourse.getPlanCount());
                                    } else if (ToolsKit.isNotEmpty(currUserCourse)) {
                                        dto.setCurrCount(currUserCourse.getCurrCount());
                                        dto.setPlanCount(currUserCourse.getPlanCount());
                                    }

                                    if (dto.getCurrCount() == 0 || dto.getCurrCount() < dto.getWordCount()) {
                                        dto.setCourseStatus(ToolsConst.STATUS_0);
                                    }
                                }
                                dtoList.add(dto);
                            }
                        }
                    }
                    wordCourses.put(childWordType.getId(), dtoList);
                }
                wordCourseDto.getTypeList().add(wordTypeDto);
            }
            int count = (int) userWordsBuService.count(userId);
            if (count > 0) {// 我的生词本有数据才显示
                // 我的生词本封装成课程信息
                WordTypeDto wordTypeDto = new WordTypeDto(Constant.DEFAULT_USER_COURSE_ID, "自定词本", WordTypeEnums.NO_SHOW.getValue());
                wordTypeDto.setSubTypeList(new ArrayList<WordTypeDto>());
                wordTypeDto.getSubTypeList()
                        .add(new WordTypeDto(Constant.DEFAULT_USER_COURSE_ID, Constant.DEFAULT_USER_COURSE_NAME, WordTypeEnums.NO_SHOW.getValue()));
                wordCourseDto.getTypeList().add(wordTypeDto);
                List<CourseInfoDto> dtoList = new ArrayList<CourseInfoDto>();
                CourseInfoDto dto = new CourseInfoDto();
                if (ToolsKit.isNotEmpty(userCourse)) {
                    dto.setCurrCourseId(userCourse.getCourseId());
                }
                dto.setCourseId(Constant.DEFAULT_USER_COURSE_ID);
                dto.setTypeName(Constant.DEFAULT_USER_COURSE_NAME);
                dto.setName(Constant.DEFAULT_USER_COURSE_NAME);
                dto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), Constant.DEFAULT_USER_COURSE_URL));
                dto.setWordCount(count);
                // 2.3版本添加 currCount 和 planCount, Update By RabyGao on 2019-12-10
                if (isNewVer) {
                    UserCourseV2 currUserCourse = userCourseBuService.getUserCourseDeletedByUserId(userId, dto.getCourseId());
                    if (userCourse != null && userCourse.getCourseId() != null && dto.getCourseId().equals(userCourse.getCourseId())) {
                        dto.setCurrCount(userCourse.getCurrCount());
                        dto.setPlanCount(userCourse.getPlanCount());
                    } else if (ToolsKit.isNotEmpty(currUserCourse)) {
                        dto.setCurrCount(currUserCourse.getCurrCount());
                        dto.setPlanCount(currUserCourse.getPlanCount());
                    }

                    if (dto.getCurrCount() == 0 || dto.getCurrCount() < dto.getWordCount()) {
                        dto.setCourseStatus(ToolsConst.STATUS_0);
                    }
                }
                dtoList.add(dto);
                wordCourses.put(Constant.DEFAULT_USER_COURSE_ID, dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        wordCourseDto.setWordCourses(wordCourses);
        // 2.8版本没有单词本则隐藏, Update By RabyGao on 2020-04-07
        if (wordCourseDto.getWordCourses() != null && wordCourseDto.getWordCourses().size() > 0) {
            List<String> delKeys = new ArrayList<>();
            for (String key:wordCourseDto.getWordCourses().keySet()) {
                List<CourseInfoDto> lists = wordCourseDto.getWordCourses().get(key);
                if (lists == null || lists.size() == 0) {
                    delKeys.add(key);
                }
            }

            for (String delKey:delKeys) {
                wordCourseDto.getWordCourses().remove(delKey);
                if (wordCourseDto.getTypeList() == null || wordCourseDto.getTypeList().size() == 0) {
                    continue;
                }
                for (WordTypeDto typeDto:wordCourseDto.getTypeList()) {
                    if (typeDto.getSubTypeList() == null || typeDto.getSubTypeList().size() == 0) {
                        continue;
                    }

                    for (int i = typeDto.getSubTypeList().size() - 1; i >= 0; i--) {
                        WordTypeDto subTypeDto = typeDto.getSubTypeList().get(i);
                        if (subTypeDto.getId().equals(delKey)) {
                            typeDto.getSubTypeList().remove(i);
                            break;
                        }
                    }
                }
            }
        }

        return wordCourseDto;
    }

    /**
     * 获取课程详情
     * 
     * @param userId
     *            用户ID
     * @param courseId
     *            课程ID
     * @return
     * @throws ServiceException
     */
    public CourseInfoDtoV2 getWordCourseInfo(String userId, String courseId) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(courseId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("课程ID不能为空");
        }
        if (Constant.DEFAULT_USER_COURSE_ID.equals(courseId)) {// 我的生词本
            return this.getUserCourseInfoDto(userId, courseId);
        } else {// 课程信息
            return this.getCourseInfoDto(userId, courseId);
        }
    }

    /**
     * 获取用户生词本课程
     * 
     * @param userId
     * @param courseId
     * @return
     */
    private CourseInfoDtoV2 getUserCourseInfoDto(String userId, String courseId) {
        UserCourseV2 userCourse = userCourseBuService.getUserCourseByUserId(userId);
        CourseInfoDtoV2 courseInfoDto = new CourseInfoDtoV2();
        courseInfoDto.setCourseId(courseId);
        courseInfoDto.setTypeName(Constant.DEFAULT_USER_COURSE_NAME);
        courseInfoDto.setName(Constant.DEFAULT_USER_COURSE_NAME);
        courseInfoDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), Constant.DEFAULT_USER_COURSE_URL));
        courseInfoDto.setWordCount((int) userWordsBuService.count(userId));
        if (ToolsKit.isNotEmpty(userCourse)) {
            courseInfoDto.setCurrCourseId(userCourse.getCourseId());
        }
        List<WordInfoDtoV2> wordInfoList = new ArrayList<WordInfoDtoV2>();// 单词信息列表
        List<UserWordsDtoV2> userWords = userWordsBuService.findUserWordsList(userId, 1, Constant.WORDS_LIST_MAX_NUM, null);
        if (ToolsKit.isNotEmpty(userWords) && userWords.size() > 0) {
            for (UserWordsDtoV2 userWordsDto : userWords) {
                WordInfoDtoV2 wordInfoDto = new WordInfoDtoV2();
                wordInfoDto.setName(userWordsDto.getName());
                wordInfoDto.setPhonetic(userWordsDto.getPhonetic());
                wordInfoDto.setDefinitions(userWordsDto.getDefinitions());
                wordInfoList.add(wordInfoDto);
            }
        }
        courseInfoDto.setWordInfoList(wordInfoList);
        return courseInfoDto;
    }

    /**
     * 获取课程信息
     * 
     * @param userId
     * @param courseId
     * @return
     */
    private CourseInfoDtoV2 getCourseInfoDto(String userId, String courseId) {
        WordCourseV2 wordCourse = jvmCacheBuService.getWordCourse(courseId);
        if (ToolsKit.isEmpty(wordCourse)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询不到该课程信息");
        }
        WordTypeV2 wordType = jvmCacheBuService.getWordType(wordCourse.getTypeId());
        UserCourseV2 userCourse = userCourseBuService.getUserCourseByUserId(userId);
        CourseInfoDtoV2 courseInfoDto = new CourseInfoDtoV2();
        courseInfoDto.setCourseId(courseId);
        courseInfoDto.setTypeName(wordCourse.getTypeName());
        courseInfoDto.setName(wordCourse.getName());
        courseInfoDto.setType(wordType.getType());
        courseInfoDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), wordCourse.getPics().get(CourseResEnums.P200x264.getKey())));
        courseInfoDto.setWordCount(wordCourse.getTotalCount());
        if (ToolsKit.isNotEmpty(userCourse)) {
            courseInfoDto.setCurrCourseId(userCourse.getCourseId());
        }
        List<WordInfoDtoV2> wordInfoList = new ArrayList<WordInfoDtoV2>();// 单词信息列表
        List<CourseInfoDtoV2> courseInfoList = new ArrayList<CourseInfoDtoV2>();// 课程信息列表
        List<String> childIdList = jvmCacheBuService.getCourseChildIdList(wordCourse.getId());// 课程单元小节集合
        go: for (String childId : childIdList) {
            Boolean hasUpdated = false;
            WordCourseV2 childCourse = jvmCacheBuService.getWordCourse(childId);
            List<WordsV2> wordsList = wordsBuService.findWordsByCourseId(childId);
            List<WordInfoDtoV2> wordInfoDtoList = new ArrayList<WordInfoDtoV2>();
            for (WordsV2 words : wordsList) {
                if (wordInfoList.size() > Constant.WORDS_LIST_MAX_NUM) {// 如果课程下单词太多，只取前600条
                    break go;
                }
                WordInfoDtoV2 wordInfoDto = new WordInfoDtoV2();
                wordInfoDto.setName(words.getWord().trim());
                wordInfoDto.setPhonetic(words.getPhonetic());
                wordInfoDto.setDefinitions(words.getDefinitions());
                wordInfoDtoList.add(wordInfoDto);
            }

            // 修正单词注释为空问题
            if (hasUpdated) {
                wordsCacheService.saveWordsList(childId, wordsList);
            }

            wordInfoList.addAll(wordInfoDtoList);
            if (wordType.getType() == WordTypeEnums.SHOW.getValue()) {// 显示课程TAB
                CourseInfoDtoV2 childDto = new CourseInfoDtoV2();
                childDto.setCourseId(childCourse.getId());
                childDto.setTypeName(childCourse.getTypeName() + childCourse.getSubName());
                childDto.setName(childCourse.getName());
                childDto.setPic(StringUtils.EMPTY);
                childDto.setWordCount(childCourse.getTotalCount());
                childDto.setWordInfoList(wordInfoDtoList);
                courseInfoList.add(childDto);
            }
        }
        courseInfoDto.setWordInfoList(wordInfoList);
        courseInfoDto.setCourseInfoList(courseInfoList);
        return courseInfoDto;
    }

    /**
     * 获取课程单词列表
     * 
     * @param userId
     *            用户ID
     * @param courseId
     *            课程ID
     * @return
     * @throws ServiceException
     */
    public List<WordInfoDtoV2> getCourseWord(String userId, String courseId) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(courseId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("课程ID不能为空");
        }
        List<WordInfoDtoV2> wordInfoList = new ArrayList<WordInfoDtoV2>();// 单词信息列表
        if (Constant.DEFAULT_USER_COURSE_ID.equals(courseId)) {// 我的生词本单词列表
            List<UserWordsDtoV2> userWords = userWordsBuService.findUserWordsList(userId, 1, Constant.WORDS_LIST_MAX_NUM, null);
            if (ToolsKit.isNotEmpty(userWords) && userWords.size() > 0) {
                for (UserWordsDtoV2 userWordsDto : userWords) {
                    WordInfoDtoV2 wordInfoDto = new WordInfoDtoV2();
                    wordInfoDto.setName(userWordsDto.getName());
                    wordInfoDto.setPhonetic(userWordsDto.getPhonetic());
                    wordInfoDto.setDefinitions(userWordsDto.getDefinitions());
                    wordInfoList.add(wordInfoDto);
                }
            }
        } else {// 课程单词列表
            WordCourseV2 wordCourse = jvmCacheBuService.getWordCourse(courseId);
            if (ToolsKit.isEmpty(wordCourse)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询不到该课程信息");
            }
            List<WordsV2> wordsList = wordsBuService.findWordsByCourseId(courseId);
            for (WordsV2 words : wordsList) {
                if (wordInfoList.size() > Constant.WORDS_LIST_MAX_NUM) {// 如果课程下单词太多，只取前600条
                    break;
                }
                WordInfoDtoV2 wordInfoDto = new WordInfoDtoV2();
                wordInfoDto.setName(words.getWord().trim());
                wordInfoDto.setPhonetic(words.getPhonetic());
                wordInfoDto.setDefinitions(words.getDefinitions());
                wordInfoList.add(wordInfoDto);
            }
        }
        return wordInfoList;
    }
}