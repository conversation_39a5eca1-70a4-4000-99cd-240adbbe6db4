package net.xplife.system.shitiku.controller;

import net.xplife.system.shitiku.service.BaiduyunService;
import net.xplife.system.shitiku.utils.Constant;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.web.core.BaseController;
import net.xplife.system.web.dto.ReturnDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author：Chenjy
 * Date:2025/8/27
 * Description:
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/baiduyun")
public class BaiduyunController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(BaiduyunController.class);

    @Autowired
    private BaiduyunService baiduyunService;

    @GetMapping("/getToken")
    public ReturnDto getToken() {
        try {
            return this.returnSuccessJson(baiduyunService.getToken());
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

}
