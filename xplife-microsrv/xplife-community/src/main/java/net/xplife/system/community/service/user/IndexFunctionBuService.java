package net.xplife.system.community.service.user;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import net.xplife.system.community.utils.ToolUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.JsonKit;
import net.xplife.system.web.config.CommonProperties;
import net.xplife.system.web.core.I18nUtils;
import net.xplife.system.community.dao.user.IndexFunctionDao;
import net.xplife.system.community.dto.user.FunctionDto;
import net.xplife.system.community.dto.user.IndexFunctionDto;
import net.xplife.system.community.entity.user.IndexFunction;
import net.xplife.system.community.enums.sq.SelectedQuestionTypeEnums;
import net.xplife.system.community.enums.user.IndexFunctionEnums;
import net.xplife.system.community.vo.user.FunctionVo;
import net.xplife.system.community.vo.user.QuestionVo;
@Service
public class IndexFunctionBuService {
    @Autowired
    private IndexFunctionDao indexFunctionDao;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private I18nUtils        i18nUtils;

    public void save(IndexFunction indexFunction) {
        indexFunctionDao.saveEntity(indexFunction);
    }

    /**
     * 更新精选题库信息
     * 
     * @param userId
     * @param subject
     * @param level
     * @param isAdd
     */
    public void updateQuestion(String userId, String subject, String level, boolean isAdd) {
        IndexFunction indexFunction = this.getDefaultIndexFunction(userId, IndexFunctionEnums.JXTK.getVersion());
        List<QuestionVo> questionList = Optional.ofNullable(indexFunction.getQuestionList()).orElseGet(() -> new ArrayList<QuestionVo>());
        boolean isExist = false;
        for (int i = 0; i < questionList.size(); i++) {
            QuestionVo questionVo = questionList.get(i);
            if (questionVo.getSubject().equals(subject)) {// 如果已存在，数值加一
                isExist = true;
                if (isAdd) {
                    questionVo.setCount(questionVo.getCount() + 1);
                } else {
                    questionVo.setCount(questionVo.getCount() - 1);
                }
            }
            questionList.set(i, questionVo);
        }
        if (!isExist) {
            int type = 0;
            for (SelectedQuestionTypeEnums selectedQuestionTypeEnums : SelectedQuestionTypeEnums.values()) {
                if (selectedQuestionTypeEnums.getKeyword().contains(subject)) {
                    type = selectedQuestionTypeEnums.getType();
                    break;
                }
            }
            questionList.add(new QuestionVo(type, type, 1, subject, level));
        }
        questionList.removeIf(item -> {// 如果数量为0，移除
            return item.getCount() <= 0;
        });
        indexFunction.setQuestionList(questionList);
        indexFunctionDao.saveEntity(indexFunction);
    }

    /**
     * 获取默认首页功能信息
     * 
     * @param userId
     * @param version
     * @return
     */
    public IndexFunction getDefaultIndexFunction(String userId, String version) {
        IndexFunction indexFunction = indexFunctionDao.findUniqueBy(IndexFunction.USER_ID_FIELD, userId);
        if (ToolsKit.isEmpty(indexFunction)) {
            IndexFunctionEnums[] indexFunctionEnums = IndexFunctionEnums.values();
            indexFunction = new IndexFunction();
            ToolsKit.setIdEntityData(indexFunction, userId);
            indexFunction.setUserId(userId);
            List<FunctionVo> typeList = new ArrayList<FunctionVo>();
            for (IndexFunctionEnums indexFunctionEnum : indexFunctionEnums) {
                if (indexFunctionEnum.getIsDefault() == ToolsConst.STATUS_1
//                        && Integer.parseInt(indexFunctionEnum.getVersion().replace(".", "")) <= Integer.parseInt(version.replace(".", ""))) {
                        && ToolUtils.compareVersion(indexFunctionEnum.getVersion(), version)) {
                    typeList.removeIf(item -> {
                        return item.getType() == indexFunctionEnum.getType();
                    });
                    typeList.add(new FunctionVo(indexFunctionEnum.getType(), indexFunctionEnum.getSort()));
                }
            }
            typeList.sort((item1, item2) -> item1.getSort() - item2.getSort());
            indexFunction.setTypeList(typeList);
            indexFunction.setFuncAddList(new ArrayList<>());
            indexFunctionDao.saveEntity(indexFunction);
        }

        if (indexFunction.getFuncAddList() == null) {
            indexFunction.setFuncAddList(new ArrayList<>());
        }
        // 表格打印排先，所有用户默认表格打印第一
        List<FunctionVo> typeList = indexFunction.getTypeList();
        boolean isExist = false;
        for (FunctionVo functionVo : typeList) {
            if (functionVo.getType() == IndexFunctionEnums.BGDY.getType()) {
                isExist = true;
                break;
            }
        }
        if (!isExist && !indexFunction.getFuncAddList().contains(IndexFunctionEnums.BGDY.getType())) {
            typeList.add(new FunctionVo(IndexFunctionEnums.BGDY.getType(), IndexFunctionEnums.BGDY.getSort()));
            indexFunction.setTypeList(typeList);
            indexFunction.getFuncAddList().add(0, IndexFunctionEnums.BGDY.getType());
            indexFunctionDao.saveEntity(indexFunction);
        }
        return indexFunction;
    }

    /**
     * 获取首页功能信息
     * 
     * @param userId
     * @param version
     * @return
     */
    public Map<String, List<IndexFunctionDto>> getIndexFunctionDto(String userId, String version, Locale locale) {
        if (ToolsKit.isEmpty(version)) {
            throw new ServiceException("获取不到版本信息");
        }
        IndexFunction indexFunction = this.getDefaultIndexFunction(userId, version);
        IndexFunctionEnums[] indexFunctionEnums = IndexFunctionEnums.values();
        List<IndexFunctionDto> userList = new ArrayList<IndexFunctionDto>();
        List<IndexFunctionDto> selection = new ArrayList<IndexFunctionDto>();
        for (IndexFunctionEnums indexFunctionEnum : indexFunctionEnums) {
//            if (Integer.parseInt(indexFunctionEnum.getVersion().replace(".", "")) <= Integer.parseInt(version.replace(".", ""))) {
            if (ToolUtils.compareVersion(indexFunctionEnum.getVersion(), version) || indexFunctionEnum.getVersion().equals(version)) {
                // 处理版本过期 Update by RabyGao 2019-10-16
                if (StringUtils.isNotBlank(indexFunctionEnum.getMaxVersion())
//                        && (Integer.parseInt(version.replace(".", "")) >= Integer.parseInt(indexFunctionEnum.getMaxVersion().replace(".", "")))) {
                        && (ToolUtils.compareVersion(indexFunctionEnum.getMaxVersion(), version))) {
                    // 当前版本大于或等于最高版本，则跳过
                    continue;
                }

                // 处理版本还没到的 Update by linnt 2020-08-18
//                if (Integer.parseInt(version.replace(".", "")) < Integer.parseInt(indexFunctionEnum.getVersion().replace(".", ""))) {
                if (!ToolUtils.compareVersion(indexFunctionEnum.getVersion(), version)) {
                    // 提前更新时，会因为前端发布延时问题，导致功能提前出线问题
                    continue;
                }

                IndexFunctionDto dto = new IndexFunctionDto();
                dto.setType(indexFunctionEnum.getType());
                String name = i18nUtils.getKey(indexFunctionEnum.getName(), locale);
                dto.setName(name);
                dto.setBigIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), indexFunctionEnum.getBigIcon()));
                dto.setSmallIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), indexFunctionEnum.getSmallIcon()));
                boolean isExist = false;
                for (FunctionVo functionVo : indexFunction.getTypeList()) {
                    if (functionVo.getType() == indexFunctionEnum.getType()) {
                        isExist = true;
                        dto.setSort(functionVo.getSort());
                        break;
                    }
                }
                if (isExist) {
                    userList.add(dto);
                } else {
                    dto.setSort(indexFunctionEnum.getSort());
                    selection.add(dto);
                }
            }
        }
        userList.sort((item1, item2) -> item1.getSort() - item2.getSort());
        selection.sort((item1, item2) -> item1.getSort() - item2.getSort());
        Map<String, List<IndexFunctionDto>> map = new HashMap<String, List<IndexFunctionDto>>();
        map.put("userList", userList);
        map.put("selection", selection);
        return map;
    }

    /**
     * 选择分配
     * 
     * @param userId
     *            用户ID
     * @param version
     *            版本号
     * @param types
     * @throws ServiceException
     */
    public void selection(String userId, String version, String types) throws ServiceException {
        if (ToolsKit.isEmpty(version)) {
            throw new ServiceException("获取不到版本信息");
        }
        if (ToolsKit.isEmpty(types)) {
            throw new ServiceException("类型集合不能为空");
        }
        List<FunctionDto> typeList = JsonKit.jsonParseArray(types, FunctionDto.class);
        Set<FunctionDto> set = new HashSet<FunctionDto>();
        set.addAll(typeList);
        List<FunctionVo> voList = new ArrayList<FunctionVo>();
        set.forEach(item -> {
            voList.add(new FunctionVo(item.getType(), item.getSort()));
        });
        IndexFunction indexFunction = this.getDefaultIndexFunction(userId, version);
        indexFunction.setTypeList(voList);
        indexFunctionDao.saveEntity(indexFunction);
    }
}