package net.xplife.system.community.interfaces.feed;

import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.community.dto.feed.AddFeedNoteDto;
import net.xplife.system.community.entity.feed.FeedNoteDelayed;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface FeedNoteDelayedService {
    @RequestMapping(value = "/community/v1/feedDelayed/getfeednotedelayedpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<FeedNoteDelayed> getFeedNoteDelayedPage(@RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize);

    @RequestMapping(value = "/community/v1/feedDelayed/addfeednotedelayed", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void addFeedNoteDelayed(AddFeedNoteDto dto, @RequestParam("userid") String userId, @RequestParam("delayedTime") String delayedTime);

    @RequestMapping(value = "/community/v1/feedDelayed/updatefeednotedelayed", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void updateFeedNoteDelayed(FeedNoteDelayed dto);

    @RequestMapping(value = "/community/v1/feedDelayed/delfeednotedelayed", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteFeedNoteDelayed(@RequestParam("feedid") String feedId);

    @RequestMapping(value = "/community/v1/feedDelayed/publish", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void publish();
}
