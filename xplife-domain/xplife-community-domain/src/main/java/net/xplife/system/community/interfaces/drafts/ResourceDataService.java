package net.xplife.system.community.interfaces.drafts;
import net.xplife.system.community.entity.drafts.Drafts;
import net.xplife.system.mongo.common.Page;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import net.xplife.system.web.core.FeignConfiguration;
import net.xplife.system.community.dto.drafts.ResourceDataDto;
/**
 * 微服务对外提供api服务层
 * 
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface ResourceDataService {
    /**
     * 获取资源数据信息
     * 
     * @return
     */
    @RequestMapping(value = "/community/v1/drafts/getresourcedata", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResourceDataDto getResourceDataById(@RequestParam("resid") String resId);

    /**
     * 查询出用户信息记录
     * 
     * @return
     */
    @RequestMapping(value = "/community/v1/drafts/saveresource", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResourceDataDto saveResource(@RequestParam("userId") String userId, @RequestParam("resUrl") String resUrl, @RequestParam("resType") int resType);

    @RequestMapping(value = "/community/v1/drafts/getdraftshistorypage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<Drafts> findDraftsPagefindPage(@RequestParam("pageno") int pageno,
                                               @RequestParam("pagesize") int pagesize,
                                               @RequestParam("otherid") String otherid,
                                               @RequestParam("subtype") String subtype,
                                               @RequestParam("length") String length,
                                               @RequestParam("printertype") String printertype,
                                               @RequestParam("begintime") String begintime,
                                               @RequestParam("endtime") String endtime);

}
