package net.xplife.system.shitiku.dao;

import net.xplife.system.mongo.core.MongoDao;
import net.xplife.system.shitiku.entity.SelectedQuestion;
import net.xplife.system.shitiku.entity.TranslationDrafts;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.util.core.ToolsKit;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
@Repository
public interface TranslationDraftsDao extends MongoRepository<TranslationDrafts, String> {

}
