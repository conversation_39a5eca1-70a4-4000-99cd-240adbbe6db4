server:
  port: 23080

spring:
  application:
    name: yoyin-shitiku
  main:
    allow-bean-definition-overriding: true
  messages:
    # 本地化语言设置
    basename: i18n/messages
    cache-seconds: 3600
    encoding: UTF-8
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
  sleuth:
    enabled: true
    http:
      legacy:
        enabled: true
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
        namespace: b67b257e-1a9f-43c0-babe-18e3a38d36c4

logging:
  level:
    net.xplife.system.shitiku: info
    com.alibaba.nacos.client.naming: warn
  config: classpath:logback-spring.xml

# 必须配置
feign:
  httpclient:
    enabled: false
  okhttp:
    enabled: true

ribbon:
  eureka:
    enabled: true
  ReadTimeout: 100000
  ConnectTimeout: 100000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 1
  OkToRetryOnAllOperations: false

hystrix:
  threadpool:
    default:
      coreSize: 1000 ##并发执行的最大线程数，默认10
      maxQueueSize: 1000 ##BlockingQueue的最大队列数
      queueSizeRejectionThreshold: 500 ##即使maxQueueSize没有达到，达到queueSizeRejectionThreshold该值后，请求也会被拒绝
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 110000

management:
  endpoints:
    web:
      exposure:
        include: '*'

# 是否开启收集日志, 默认false
event:
  log:
    enable: false

# 是否开启使用 MongoDB
mongo:
  enable: true

# MongoDB 数据库配置
mongoReplicaSet: **************:38749
mongoUserName: yoyinadmin
mongoPassword: 123@yoyin
mongoDatabase: gam
mongoPrint: true

# Redis 配置
redisHost: **************
redisPort: 6988
redisPassword: 123@yoyin
redisDatabase: 0

# common 常用配置
apiDomain: https://api.yoyin.net
fileDomain: https://m.yoyin.net
shareDomain: https://share.yoyin.net
h5Env:
userDefaultPic: /api/img/gam/common/common_ic_defaultavatar.png
codeIdNum: 0
defaultName: 星星机
androidQQAppId: 1107873591
androidQQAppKey: yUKfENLm8ta5ihjL
iosQQAppId: 1107873591
iosQQAppKey: yUKfENLm8ta5ihjL
wxAppId: wx0b87e74c7463125e
wxAppSecret: d723236c7fc904ab8b4c899662fb2d8e
h5WxAppId: wx92d53b2b360320e1
h5WxAppSecret: 79489ab0a4089543fd4c4d64c4e3bb5a
h5QqAppId: 101526977
h5QqAppSecret: 2e9a7382ddda7027cdfaf9e142467166

#阿里云第三方服务商接口秘钥
#天壤去字迹
aliyun:
  papertr:
    appKey: 204839008
    appSecret: jShnNmmvFBJRZ0MqWbXdTdK9ucGRHB09
    host: papertr.market.alicloudapi.com
    path: /sjsxcc
    method: POST

#百度云第三方服务商接口秘钥
baiduyun:
  oauth:
    appKey: niPBLoOXtDRdVx5yjqfotanV
    appSecret: AP7bqYqy3hhiwkofRhdbfYlLyVrznRGy
    host: https://aip.baidubce.com/oauth/2.0
    path: /token