package net.xplife.system.quanpin.service;

import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.quanpin.dao.MemberGoodsDao;
import net.xplife.system.quanpin.dao.MemberOrderDao;
import net.xplife.system.quanpin.dao.PaperViewHistoryDao;
import net.xplife.system.quanpin.dto.MemberOrderDto;
import net.xplife.system.quanpin.entity.MemberGoods;
import net.xplife.system.quanpin.entity.MemberOrder;
import net.xplife.system.quanpin.entity.PaperViewHistory;
import net.xplife.system.quanpin.enums.GoodsUnitTypeEnums;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/11 11:08
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class MemberOrderBuService {
    private final static String CACHE_KEY_MEMBER_DAY = "quanpin:member:day:%s:%s"; // 每日的会员信息
    private final static String CACHE_KEY_LIMIT_COUNT_DAY = "quanpin:member:limit:day:%s:%s"; // 每日每个人打印情况
    private final static String CACHE_KEY_GOODSID_USER = "quanpin:member:goodsid:userid:%s:%s";// 当前用户购买的特种商品情况

    private final static int MAX_PRINT_NUM_MEMBER = 15;
    private final static int MAX_PRINT_NUM_GUEST = 5;

    @Autowired
    private MemberOrderDao memberOrderDao;
    @Autowired
    private MemberGoodsDao memberGoodsDao;
    @Autowired
    private PaperViewHistoryDao paperViewHistoryDao;

    @Autowired
    private MemberAnalysisBuService memberAnalysisBuService;

    public void createOrderByCond(String orderNo, String goodsId, String source, String userId, String remarks){
        MemberGoods memberGoods = memberGoodsDao.getById(goodsId, ToolsConst.DATA_SUCCESS_STATUS);

        if (memberGoods.isOnlyOnceFlag() && this.checkExistOrderByGoodsId(goodsId, userId)) {
            throw new ServiceException("抱歉，当前商品是限购商品，您的购买次数已用完。");
        }

        MemberOrder order = new MemberOrder();
        order.setTitle("VIP会员-" + memberGoods.getTitle());
        order.setBeginTime(new Date());
        order.setPaySource(source);
        order.setGoodsName(memberGoods.getTitle());
        order.setGoodsPrice(memberGoods.getRealPrice());
        order.setGoodsId(memberGoods.getId());
        order.setOrderStatus("未支付");
        order.setRemarks(remarks);

        Calendar nextCalendar = null;
        // 这里应该是以当前会员购买最后到期的时间做为开始时间
        Map<String, Object> memberInfo = this.memberInfoByUser(userId);
        if (memberInfo!=null && ((Boolean)memberInfo.get("memberFlag"))){
            order.setBeginTime(ToolsKit.Date.parseDate(String.valueOf(memberInfo.get("memberDate"))));
            nextCalendar = ToolsKit.Date.calendar(order.getBeginTime());
        } else {
            nextCalendar = Calendar.getInstance();
        }
        if (GoodsUnitTypeEnums.DAY.getKey().equals(memberGoods.getUnit())){
            nextCalendar.add(Calendar.DATE, memberGoods.getAmount());
        } else if (GoodsUnitTypeEnums.MONTH.getKey().equals(memberGoods.getUnit())){
            nextCalendar.add(Calendar.MONTH, memberGoods.getAmount());
        } else if (GoodsUnitTypeEnums.YEAR.getKey().equals(memberGoods.getUnit())){
            nextCalendar.add(Calendar.YEAR, memberGoods.getAmount());
        } else {
            nextCalendar.add(Calendar.DATE, memberGoods.getAmount());
        }
        order.setEndTime(ToolsKit.Date.date(nextCalendar));

        order.setOrderNo(orderNo);
        ToolsKit.setIdEntityData(order, userId);
        order.setStatus(ToolsConst.DATA_DELETE_STATUS);
        memberOrderDao.saveEntity(order);
    }


    // 创建临时的用户订单（status为“已删除”）orderStatus改成未支付
    // 根据orderNo，把“status”修改成“审核通过”,orderStatus改成已支付
    public void updatePaidStatus(String orderNo){
        List<MemberOrder> orderList = memberOrderDao.findListByCond(1,10, null, orderNo, null, ToolsConst.DATA_DELETE_STATUS);
        if (ToolsKit.isEmpty(orderList)){
            return;
        }
        MemberOrder order = orderList.get(0);
        order.setStatus(ToolsConst.DATA_SUCCESS_STATUS);
        order.setOrderStatus("已支付");
        memberOrderDao.saveEntity(order);

        // 支付完后，删除此缓存
        String key = String.format(CACHE_KEY_MEMBER_DAY, order.getCreateuserid(), ToolsKit.Date.formatDate(new Date()));
        String key2 = String.format(CACHE_KEY_LIMIT_COUNT_DAY, order.getCreateuserid(), ToolsKit.Date.formatDate(new Date()));
        CacheKit.cache().del(key);
        CacheKit.cache().del(key2);

        ToolsKit.Thread.execute(new Runnable() {
            @Override
            public void run() {
                memberAnalysisBuService.updateByUserId(order.getCreateuserid(), order.getEndTime(), Boolean.FALSE);
            }
        });
    }

    public boolean checkExistOrderNo(String orderNo) {
        List<MemberOrder> orderList = memberOrderDao.findListByCond(1,10, null, orderNo, null, null);
        if (ToolsKit.isNotEmpty(orderList)){
            return true;
        } else {
            return false;
        }
    }

    // 获取当前用户的所有已审核通过的订单
    public List<MemberOrderDto> findList(int pageNo, int pageSize, String userId){
        List<MemberOrderDto> result = new ArrayList<>();
        List<MemberOrder> list = memberOrderDao.findListByCond(pageNo, pageSize, null, null, userId, ToolsConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isNotEmpty(list)){
            for (MemberOrder memberOrder: list) {
                MemberOrderDto dto = new MemberOrderDto();
                ToolsKit.Bean.copyProperties(memberOrder, dto);
                dto.setTimeHorizon(ToolsKit.Date.formatDate(memberOrder.getBeginTime()) + "-" + ToolsKit.Date.formatDate(memberOrder.getEndTime()));
                Long currTime = new Date().getTime();
//                if (currTime>=memberOrder.getBeginTime().getTime() && currTime<memberOrder.getEndTime().getTime()){
                if (currTime<memberOrder.getEndTime().getTime()){
                    dto.setOrderStatus(1);
                    dto.setOrderStatusName("服务中");
                } else {
                    dto.setOrderStatus(0);
                    dto.setOrderStatusName("已过期");
                }
                dto.setCreateTime(ToolsKit.Date.formatDateTime(memberOrder.getCreatetime()));

                if (ToolsKit.isEmpty(dto.getTitle())){
                    dto.setTitle("VIP会员-" + memberOrder.getGoodsName());
                }

                result.add(dto);
            }
        }
        return result;
    }

    // 获取当前用户的所有已审核通过的订单
    public List<MemberOrderDto> findList2(int pageNo, int pageSize, String userId){
        List<MemberOrderDto> result = new ArrayList<>();
        List<MemberOrder> list = memberOrderDao.findListByCond(pageNo, pageSize, null, null, userId, null);
        if (ToolsKit.isNotEmpty(list)){
            for (MemberOrder memberOrder: list) {
                MemberOrderDto dto = new MemberOrderDto();
                ToolsKit.Bean.copyProperties(memberOrder, dto);
                dto.setTimeHorizon(ToolsKit.Date.formatDate(memberOrder.getBeginTime()) + "-" + ToolsKit.Date.formatDate(memberOrder.getEndTime()));
                Long currTime = new Date().getTime();
                if (ToolsConst.DATA_DELETE_STATUS.equals(memberOrder.getStatus())){
                    dto.setOrderStatus(2);
                    dto.setOrderStatusName("未支付");
                } else if (currTime>=memberOrder.getBeginTime().getTime() && currTime<memberOrder.getEndTime().getTime()){
                    dto.setOrderStatus(1);
                    dto.setOrderStatusName("服务中");
                } else {
                    dto.setOrderStatus(0);
                    dto.setOrderStatusName("已过期");
                }
                result.add(dto);
            }
        }
        return result;
    }

    // 判断当前用户的所有已完成的订单中，是否包含了  goodsId的商品
    public boolean checkExistOrderByGoodsId(String goodsId, String userId) {
        String key = String.format(CACHE_KEY_GOODSID_USER, goodsId, userId);
        System.out.println(key);
        System.out.println(CacheKit.cache().get(key, String.class));
//        if (CacheKit.cache().exists(key)){
//            String result = CacheKit.cache().get(key, String.class);
//            if ("true".equals(result)){
//                return true;
//            } else {
//                return false;
//            }
//        }

        List<MemberOrder> list = memberOrderDao.findListByCond(1, 10, goodsId,null, userId, ToolsConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isEmpty(list)){
            CacheKit.cache().set(key, "false", ToolsConst.MONTH_SECOND);
            return false;
        }
        CacheKit.cache().set(key, "true", ToolsConst.MONTH_SECOND);
        return true;
    }

    public Map memberInfoByUser(String userid) {
        Map<String, Object> result = new HashMap<>();
        boolean memberFlag = false;
        String memberDate = "";

        String key = String.format(CACHE_KEY_MEMBER_DAY, userid, ToolsKit.Date.formatDate(new Date()));
        if (CacheKit.cache().exists(key)){
            return CacheKit.cache().get(key, Map.class);
        }


        List<MemberOrder> list = memberOrderDao.findListByCond(0,10,null,null,userid, ToolsConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isNotEmpty(list)){
            MemberOrder order = list.get(0);
            if (order.getEndTime().getTime()>new Date().getTime()){
                memberFlag = true;
                memberDate = ToolsKit.Date.formatDate(order.getEndTime());
            } else {
                memberFlag = false;
                memberDate = ToolsKit.Date.formatDate(order.getEndTime());
            }
        }
        result.put("memberFlag", memberFlag);
        result.put("memberDate", memberDate);
        CacheKit.cache().set(key, result, ToolsConst.DAY_SECOND);
        return result;
    }

    public Map<String, Object> canPrint(String userId, String paperIdEnc) {
        Map<String, Object> result = new HashMap<>();
        result.put("overRead", checkIsReadOver(userId, paperIdEnc));
        String key = String.format(CACHE_KEY_LIMIT_COUNT_DAY, userId, ToolsKit.Date.formatDate(new Date()));

        if (CacheKit.cache().exists(key)){
            Map map = CacheKit.cache().get(key, Map.class);
            map.put("overRead", checkIsReadOver(userId, paperIdEnc));
            // 如果能直接打印，就直接返回
            if (((Boolean)map.get("canPrint")).booleanValue()) {
                return map;
            } else {
                // 如果paperIdEnc有值，则判断它是否存在，存在说明之前已经打开过，就标记已查看过，overRead
                if (ToolsKit.isEmpty(paperIdEnc)){
                    return map;
                } else {
                    map.put("overRead", checkIsReadOver(userId, paperIdEnc));
                    return map;
                }
            }

        }

//        Map<String, Object> memberInfo = this.memberInfoByUser(userId);
//        if (((Boolean) memberInfo.get("memberFlag")).booleanValue()){
//            // 判断是否为会员，如果是会员，则判断是否已打15次
//            int count = paperViewHistoryDao.countByUserId(userId, ToolsKit.Date.formatDate(new Date()), 1);
//            if(count>=50){
//                result.put("canPrint", false);
//                result.put("num", 0);
//                result.put("overRead", checkIsReadOver(userId, paperIdEnc));
//            } else {
//                result.put("canPrint", true);
//                result.put("num", 50-count);
//                result.put("overRead", false);
//            }
//        } else {
//            // 不是会员，判断是否已打5次
//            int count = paperViewHistoryDao.countByUserId(userId, null, 0);
//            if(count>=50){
//                result.put("canPrint", false);
//                result.put("num", 0);
//            } else {
//                result.put("canPrint", true);
//                result.put("num", 50-count);
//            }
//        }
        // 判断是否为会员，如果是会员，则判断是否已打15次
        int count = paperViewHistoryDao.countByUserId(userId, ToolsKit.Date.formatDate(new Date()), null);
        System.out.println(String.format("判断当前用户的查询次数：用户ID=%s， 日期=%s， 次数=%s", userId, ToolsKit.Date.formatDate(new Date()), count) );
        if(count>=50){
            result.put("canPrint", false);
            result.put("num", 0);
            result.put("overRead", checkIsReadOver(userId, paperIdEnc));
        } else {
            result.put("canPrint", true);
            result.put("num", 50-count);
            result.put("overRead", checkIsReadOver(userId, paperIdEnc));
        }

        CacheKit.cache().set(key, result, ToolsConst.DAY_SECOND);

        return result;
    }

    private Boolean checkIsReadOver(String userId, String paperIdEnc) {
        if (ToolsKit.isEmpty(paperIdEnc)){
            return false;
        }
        PaperViewHistory paperViewHistory = paperViewHistoryDao.findOneByPaperIdEnc(paperIdEnc, userId, null);
        if (paperViewHistory == null || ToolsKit.isEmpty(paperViewHistory.getId())){
            return false;
        } else {
            return true;
        }
    }

    public void insertPaperViewHistory(String paperIdEnc, String userId) {
        // 判断当前是否是会员，非会员的情况下，必须添加
        Map<String, Object> memberInfo = memberInfoByUser(userId);
        boolean isMember = (Boolean)memberInfo.get("memberFlag");

        PaperViewHistory paperViewHistory = paperViewHistoryDao.findOneByPaperIdEnc(paperIdEnc, userId, null);
        if (paperViewHistory == null || ToolsKit.isEmpty(paperViewHistory.getId())){
            paperViewHistory = new PaperViewHistory();
            // 如果新增，则加1
            ToolsKit.Thread.execute(new Runnable() {
                @Override
                public void run() {
                    memberAnalysisBuService.addFatchCount(userId);
                }
            });
        } else  {
            if ((Boolean)memberInfo.get("memberFlag")){
                // 存在历史，则不处理
                return;
            } else {
                paperViewHistory = new PaperViewHistory();
            }
        }

        if (isMember){
            paperViewHistory.setType(1);
        } else {
            paperViewHistory.setType(0);
        }

        ToolsKit.setIdEntityData(paperViewHistory, userId);
        paperViewHistory.setUserId(userId);
        paperViewHistory.setPaperIdEnc(paperIdEnc);
        paperViewHistory.setViewDate(ToolsKit.Date.formatDate(new Date()));
        paperViewHistoryDao.saveEntity(paperViewHistory);
        // 清除一下缓存
        String key = String.format(CACHE_KEY_LIMIT_COUNT_DAY, userId, ToolsKit.Date.formatDate(new Date()));
        CacheKit.cache().del(key);
    }

    public void printPaper(String paperIdEnc, String userId) {
        PaperViewHistory paperViewHistory = paperViewHistoryDao.findOneByPaperIdEnc(paperIdEnc, userId, null);
        if (paperViewHistory!=null){
            // 正常情况下都会有数据
            paperViewHistory.setPrintCount(paperViewHistory.getPrintCount()+1);
            paperViewHistoryDao.saveEntity(paperViewHistory);
        }
        // 打印次数+1
        ToolsKit.Thread.execute(new Runnable() {
            @Override
            public void run() {
                memberAnalysisBuService.addPrintCount(userId);
            }
        });
    }

    public String updateOrderStatusById(String id) {
        MemberOrder order = memberOrderDao.getById(id, ToolsConst.DATA_DELETE_STATUS);

        if (order==null){
            return "未找到该订单";
        }

        order.setStatus(ToolsConst.DATA_SUCCESS_STATUS);
        order.setOrderStatus("已支付");
        memberOrderDao.saveEntity(order);

        // 支付完后，删除此缓存
        String key = String.format(CACHE_KEY_MEMBER_DAY, order.getCreateuserid(), ToolsKit.Date.formatDate(new Date()));
        String key2 = String.format(CACHE_KEY_LIMIT_COUNT_DAY, order.getCreateuserid(), ToolsKit.Date.formatDate(new Date()));
        String key3 = String .format(CACHE_KEY_GOODSID_USER, order.getGoodsId(), order.getCreateuserid());
        CacheKit.cache().del(key);
        CacheKit.cache().del(key2);
        CacheKit.cache().del(key3);
        return "修改成功";
    }

    public void clearCache(String goodsId, String userId){
        // 支付完后，删除此缓存
        String key = String.format(CACHE_KEY_MEMBER_DAY, userId, ToolsKit.Date.formatDate(new Date()));
        String key2 = String.format(CACHE_KEY_LIMIT_COUNT_DAY, userId, ToolsKit.Date.formatDate(new Date()));
        String key3 = String .format(CACHE_KEY_GOODSID_USER, goodsId, userId);
        CacheKit.cache().del(key);
        CacheKit.cache().del(key2);
        CacheKit.cache().del(key3);
    }

    /** --------------------------------------------------------------支撑平台-------------------------------------------------------------------------*/
    /***
     *
     * @param pageNo
     * @param pageSize
     * @param createUserId
     * @param status
     * @param payBeginDate
     * @param payEndDate
     * @param sortColumn
     * @param sortDesc
     * @return
     */
    public Page<MemberOrder> findPage(int pageNo, int pageSize, String orderNo,String goodsId, String createUserId, String status, String payBeginDate, String payEndDate, String sortColumn, String sortDesc) {
        return memberOrderDao.findPageByCond(pageNo, pageSize, orderNo, goodsId, createUserId, status, payBeginDate, payEndDate, sortColumn, sortDesc);
    }

    /***
     * 根据id获取删除状态的订单
     * @param id
     * @return
     */
    public MemberOrder findDeleleRecordById(String id) {
        return memberOrderDao.getById(id, ToolsConst.DATA_DELETE_STATUS);
    }

    /***
     * 把day天前的未支付的记录，删除掉
     * @param day
     * @return
     */
    public String cleanNoPayOrderRecordByDay(int day) {
        Date currDate = new Date();
        List<MemberOrder> list = memberOrderDao.findListBeforeDateWithNoPay(ToolsKit.Date.offsetDay(currDate, -day));
        if (ToolsKit.isNotEmpty(list)){
            list.forEach(item->{
                memberOrderDao.removeById(item.getId());
            });
        }
        return "SUCCESS";
    }
}
