package net.xplife.system.shitiku.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/8/26
 * Description:
 */
@Data
public class TranslationPrintHistoryBatchDeleteDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要删除的ID列表
     */
    @NotEmpty(message = "打印历史ID列表不能为空")
    private List<String> ids;
}
