package net.xplife.system.community.interfaces.system;

import net.xplife.system.community.entity.system.FunctionsSetting;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/5/31 8:52
 * @description：
 * @modified By：
 * @version: $
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface FunctionsSettingService {

    @RequestMapping(value = "/community/v1/functionssetting/deletebyid", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteById(@RequestParam("id") String id);

    @RequestMapping(value = "/community/v1/functionssetting/saveorupdate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void  saveOrUpdate(@RequestBody FunctionsSetting item);

    @RequestMapping(value = "/community/v1/functionssetting/findlistpl", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<FunctionsSetting> findList(@RequestParam("type") String type, @RequestParam("printerType") String printerType);

}
