package net.xplife.system.community.controller.feed;
import net.xplife.system.web.dto.HeadInfoDto;
//import net.xplife.system.coin.enums.MissionCodeEnums;
//import net.xplife.system.coin.interfaces.CoinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.core.BaseController;
import net.xplife.system.web.dto.ReturnDto;
import net.xplife.system.community.dto.feed.SendCommentDto;
import net.xplife.system.community.dto.feed.SendReplyDto;
import net.xplife.system.community.service.feed.CommentReplyBuService;
import net.xplife.system.community.service.feed.FeedCommentBuService;
import net.xplife.system.community.utils.Constant;
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/comment")
public class FeedCommentController extends BaseController {
    @Autowired
    private FeedCommentBuService  feedCommentBuService;
    @Autowired
    private CommentReplyBuService commentReplyBuService;
//    @Autowired
//    private CoinService coinService;

    /**
     * 获取评论列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getcommentlist")
    public ReturnDto getCommentList() {
        try {
            String feedId = this.getValue("feedid");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String lastId = this.getValue("lastid");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    feedCommentBuService.getCommentList(feedId, Integer.parseInt(pageNo), Integer.parseInt(pageSize), lastId, true));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取评论详情
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getcomminfo")
    public ReturnDto getCommInfo() {
        try {
            String commId = this.getValue("commid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, feedCommentBuService.getCommInfo(commId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取回复列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getreplylist")
    public ReturnDto getReplyList() {
        try {
            String commId = this.getValue("commid");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            String lastId = this.getValue("lastid");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    commentReplyBuService.getReplyList(commId, Integer.parseInt(pageNo), Integer.parseInt(pageSize), lastId));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除评论
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/delcomm")
    public ReturnDto delComm() {
        try {
            String id = this.getValue("id");
            feedCommentBuService.del(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除回复
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/delreply")
    public ReturnDto delReply() {
        try {
            String id = this.getValue("id");
            commentReplyBuService.delReply(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 发布评论
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/sendcomment")
    public ReturnDto sendComment() {
        try {
            String userId = this.getValue("userId");
            SendCommentDto sendCommentDto = this.getBean(SendCommentDto.class);
            HeadInfoDto headInfoDto = this.getHeadInfoDto();

            // 添加积分
//            try{
//                coinService.finishMission(MissionCodeEnums.ADDCOMMENTNOTE.getCode());
//            }catch(Exception e){
//                System.err.println(e.getMessage());
//            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, feedCommentBuService.sendComment(userId, sendCommentDto, headInfoDto.getVersion()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 发布回复
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/sendreply")
    public ReturnDto sendReply() {
        try {
            String userId = this.getValue("userId");
            SendReplyDto sendReplyDto = this.getBean(SendReplyDto.class);
            HeadInfoDto headInfoDto = this.getHeadInfoDto();

            // 添加积分
//            try{
//                coinService.finishMission(MissionCodeEnums.ADDCOMMENTNOTE.getCode());
//            }catch(Exception e){
//                System.err.println(e.getMessage());
//            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, commentReplyBuService.sendReply(userId, sendReplyDto, headInfoDto.getVersion()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
