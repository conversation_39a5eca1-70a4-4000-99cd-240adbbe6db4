package net.xplife.system.shitiku.util;

import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * Author：Chenjy
 * Date:2025/8/7
 * Description:
 */
@Component
public class ThirdPartyCallUtil {
    // 记录每个接口的最后调用时间
    private final ConcurrentHashMap<String, AtomicLong> lastCallTimes = new ConcurrentHashMap<>();

    /**
     * 带频率控制的第三方接口调用
     *
     * @param apiName       接口名称
     * @param minIntervalMs 最小间隔时间（毫秒）
     * @param callable      实际调用逻辑
     * @return 调用结果
     */
    public <T> T callWithInterval(String apiName, long minIntervalMs, ThirdPartyCallable<T> callable) {
        AtomicLong lastCallTime = lastCallTimes.computeIfAbsent(apiName, k -> new AtomicLong(0));

        while (true) {
            long currentTime = System.currentTimeMillis();
            long lastTime = lastCallTime.get();
            long timeSinceLastCall = currentTime - lastTime;

            if (timeSinceLastCall >= minIntervalMs) {
                // 尝试更新最后调用时间
                if (lastCallTime.compareAndSet(lastTime, currentTime)) {
                    try {
                        return callable.call();
                    } catch (Exception e) {
                        throw new RuntimeException("调用第三方接口失败: " + apiName, e);
                    }
                }
                // 如果更新失败，说明有其他线程同时更新，继续循环
            } else {
                // 需要等待
                long waitTime = minIntervalMs - timeSinceLastCall;
                try {
                    Thread.sleep(waitTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("等待被中断: " + apiName, e);
                }
            }
        }
    }

    /**
     * 带随机延迟的调用（更自然，避免固定模式）
     *
     * @param apiName       接口名称
     * @param minIntervalMs 最小间隔时间（毫秒）
     * @param maxRandomMs   最大随机延迟（毫秒）
     * @param callable      实际调用逻辑
     * @return 调用结果
     */
    public <T> T callWithRandomInterval(String apiName, long minIntervalMs, long maxRandomMs, ThirdPartyCallable<T> callable) {
        AtomicLong lastCallTime = lastCallTimes.computeIfAbsent(apiName, k -> new AtomicLong(0));

        while (true) {
            long currentTime = System.currentTimeMillis();
            long lastTime = lastCallTime.get();
            long timeSinceLastCall = currentTime - lastTime;

            // 计算随机延迟
            long randomDelay = (long) (Math.random() * maxRandomMs);
            long totalInterval = minIntervalMs + randomDelay;

            if (timeSinceLastCall >= totalInterval) {
                if (lastCallTime.compareAndSet(lastTime, currentTime)) {
                    try {
                        return callable.call();
                    } catch (Exception e) {
                        throw new RuntimeException("调用第三方接口失败: " + apiName, e);
                    }
                }
            } else {
                long waitTime = totalInterval - timeSinceLastCall;
                try {
                    Thread.sleep(waitTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("等待被中断: " + apiName, e);
                }
            }
        }
    }

    /**
     * 批量调用第三方接口
     *
     * @param apiName       接口名称
     * @param items         待处理的数据
     * @param minIntervalMs 最小间隔时间（毫秒）
     * @param processor     处理函数
     * @return 处理结果列表
     */
    public <T, R> java.util.List<R> batchCall(String apiName, java.util.List<T> items,
                                              long minIntervalMs, java.util.function.Function<T, R> processor) {
        java.util.List<R> results = new java.util.ArrayList<>();

        for (T item : items) {
            R result = callWithInterval(apiName, minIntervalMs, () -> processor.apply(item));
            results.add(result);
        }

        return results;
    }

    /**
     * 获取接口调用统计信息
     *
     * @param apiName 接口名称
     * @return 统计信息
     */
    public String getCallStats(String apiName) {
        AtomicLong lastCallTime = lastCallTimes.get(apiName);
        if (lastCallTime == null) {
            return "接口 " + apiName + " 尚未调用";
        }

        long lastTime = lastCallTime.get();
        long currentTime = System.currentTimeMillis();
        long timeSinceLastCall = currentTime - lastTime;

        return String.format("接口: %s, 最后调用时间: %s, 距离上次调用: %d ms",
                apiName,
                LocalDateTime.now().minus(timeSinceLastCall, ChronoUnit.MILLIS),
                timeSinceLastCall);
    }

    /**
     * 第三方接口调用接口
     */
    @FunctionalInterface
    public interface ThirdPartyCallable<T> {
        T call() throws Exception;
    }
}
