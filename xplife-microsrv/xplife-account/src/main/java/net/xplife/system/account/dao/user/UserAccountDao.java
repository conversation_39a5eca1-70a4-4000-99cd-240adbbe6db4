package net.xplife.system.account.dao.user;
import java.util.Date;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import net.xplife.system.account.entity.user.UserAccount;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.mongo.core.MongoDao;
import net.xplife.system.tools.util.core.ToolsKit;
@Repository
public class UserAccountDao extends MongoDao<UserAccount> {
    /**
     * 根据用户ID查询用户账号信息
     *
     * @param id
     *            用户ID
     * @return 用户账号信息
     * @throws Exception
     */
    public UserAccount getUserAccountById(String id) throws Exception {
        Query query = new Query();
        query.addCriteria(Criteria.where(UserAccount.ID_FIELD).is(id));
        query.addCriteria(Criteria.where(UserAccount.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return this.findOne(query);
    }

    /**
     * 根据账号查询用户账号信息
     *
     * @param account
     *            账号
     * @return 用户账号信息
     * @throws Exception
     */
    public UserAccount getUserAccountByAccount(String propertyName, String account) throws Exception {
        Query query = new Query();
        query.addCriteria(Criteria.where(propertyName).is(account));
        query.addCriteria(Criteria.where(UserAccount.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        query.fields().include(UserAccount.ID_FIELD);
        return this.findOne(query);
    }

    /**
     * 统计数量
     * 
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @return
     */
    public long getDaysCount(Date startDate, Date endDate) {
        Query query = new Query();
        if (ToolsKit.isNotEmpty(startDate) && ToolsKit.isNotEmpty(endDate)) {
            query.addCriteria(new Criteria().andOperator(Criteria.where(UserAccount.CREATETIME_FIELD).gte(startDate),
                    Criteria.where(UserAccount.CREATETIME_FIELD).lte(endDate)));
        }
        query.addCriteria(Criteria.where(UserAccount.STATUS_FIELD).is(ToolsConst.DATA_SUCCESS_STATUS));
        return this.count(query);
    }
}