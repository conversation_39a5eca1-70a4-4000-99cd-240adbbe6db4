package net.xplife.system.platform.community.service.feed;
import java.util.*;

import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.coin.enums.MissionCodeEnums;
import net.xplife.system.coin.interfaces.CoinService;
import net.xplife.system.community.enums.feed.FeedNoteStickEnums;
import net.xplife.system.community.enums.feed.FeedStatisticsTypeEnums;
import net.xplife.system.community.enums.studynote.StudyNoteGradeEnum;
import net.xplife.system.community.enums.studynote.StudyNoteSubjectEnum;
import net.xplife.system.platform.community.service.label.LabelBuService;
import net.xplife.system.friends.interfaces.FriendsFollowService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.xplife.system.account.dto.user.UserLoginDto;
import net.xplife.system.account.interfaces.user.UserAccountService;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.WebKit;
import net.xplife.system.web.config.CommonProperties;
import net.xplife.system.web.dto.PicInfoDto;
import net.xplife.system.platform.community.cache.feed.FeedNoteCacheService;
import net.xplife.system.platform.community.dao.feed.FeedNoteDao;
import net.xplife.system.community.dto.feed.AddFeedNoteDto;
import net.xplife.system.community.dto.feed.FeedCommentDto;
import net.xplife.system.community.dto.feed.FeedInfoDto;
import net.xplife.system.community.dto.feed.PicDto;
import net.xplife.system.community.entity.feed.FeedNote;
import net.xplife.system.community.entity.feed.FeedStatistics;
import net.xplife.system.community.entity.feed.LikeFeed;
import net.xplife.system.community.entity.label.Label;
import net.xplife.system.community.enums.feed.IndexFeedTypeEnums;
import net.xplife.system.platform.community.service.jvm.JvmCacheBuService;
import net.xplife.system.platform.community.service.label.LabelFeedBuService;
import net.xplife.system.community.utils.Constant;
import net.xplife.system.community.utils.ToolUtils;
import net.xplife.system.community.vo.feed.FeedStatisticsVo;
import net.xplife.system.community.vo.feed.PicVo;
import net.xplife.system.friends.dto.FriendStatusDto;
import net.xplife.system.friends.enums.FriendsStatusTypeEnums;
import net.xplife.system.friends.interfaces.FriendsService;
import cn.hutool.crypto.SecureUtil;

import static cn.hutool.core.thread.ThreadUtil.sleep;

@Service
public class FeedNoteBuService {
    @Autowired
    private FeedNoteDao             feedNoteDao;
    @Autowired
    private FeedNoteCacheService    feedNoteCacheService;
    @Autowired
    private UserAccountService      userAccountService;
    @Autowired
    private CommonProperties        commonProperties;
    @Autowired
    private JvmCacheBuService       jvmCacheBuService;
    @Autowired
    private FeedStatisticsBuService feedStatisticsBuService;
    @Autowired
    private FriendsService          friendsService;
    @Autowired
    private FriendsFollowService    friendsFollowService;
    @Autowired
    private LikeFeedBuService       likeFeedBuService;
    @Autowired
    private IndexFeedBuService      indexFeedBuService;
    @Autowired
    private UserFeedBuService       userFeedBuService;
    @Autowired
    private LabelFeedBuService      labelFeedBuService;
    @Autowired
    private FeedCommentBuService    feedCommentBuService;
    @Autowired
    private GiveLikeFeedBuService   giveLikeFeedBuService;
    @Autowired
    private LabelBuService labelBuService;
    @Autowired
    private CoinService coinService;

    public void save(FeedNote feedNote) {
        feedNoteDao.saveEntity(feedNote);
        feedNoteCacheService.saveFeedNote(feedNote);
    }

    /**
     * 根据ID获取动态详情对象
     * 
     * @param id
     *            ID
     * @return 动态详情对象
     * @throws ServiceException
     */
    public FeedNote getFeedNoteById(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        FeedNote feedNote = feedNoteCacheService.getFeedNote(id);
        if (ToolsKit.isEmpty(feedNote)) {
            try {
                feedNote = feedNoteDao.getFeedNoteById(id);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(feedNote)) {
                feedNoteCacheService.saveFeedNote(feedNote);
            }
        }
        return feedNote;
    }

    /**
     * 获取动态信息dto
     * 
     * @param userId
     *            用户ID
     * @param userLoginDto
     *            动态ID
     * @return
     * @throws ServiceException
     */
    public FeedInfoDto getFeedInfoDto(String userId, UserLoginDto userLoginDto, FeedNote feedNote, boolean isMe) throws ServiceException {
        // 获取labelId中，是“学习”的
        String studyLabelId = labelBuService.getLabelListStudy();

        if (ToolsKit.isNotEmpty(feedNote)) {
            FeedInfoDto dto = new FeedInfoDto();
            dto.setUserId(feedNote.getCreateuserid());
            if (ToolsKit.isNotEmpty(userLoginDto)) {
                dto.setUserName(userLoginDto.getUserInfoDto().getNickName());
                dto.setUserPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), userLoginDto.getUserInfoDto().getUserPic()));
                dto.setSex(userLoginDto.getUserInfoDto().getSex());
                dto.setCodeId(userLoginDto.getUserInfoDto().getCodeId());
                dto.setIsOfficial(userLoginDto.getUserInfoDto().getIsOfficial());
                dto.setUserTitleObj(userLoginDto.getUserInfoDto().getUserTitleObj());
            }
            FeedStatistics feedStatistics = feedStatisticsBuService.getFeedStatistics(feedNote.getId());
            List<PicDto> picDtoList = new ArrayList<PicDto>();
            List<PicDto> smallPicDtoList = new ArrayList<PicDto>();
            List<PicVo> pic = feedNote.getPic();
            if (ToolsKit.isNotEmpty(pic)) {
                for (int i = 0; i < pic.size(); i++) {
                    PicDto picDto = this.getPicDto(feedNote.getId(), userId, pic.get(i));
                    String tmpPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), pic.get(i).getPic());
                    String flag = SecureUtil.md5(tmpPic);
                    picDto.setLikeNum(this.getOneStatisticsNum(feedStatistics.getLikeVo(), flag));
                    picDto.setDownLoadNum(this.getOneStatisticsNum(feedStatistics.getDownLoadVo(), flag));
                    picDto.setPrintNum(this.getOneStatisticsNum(feedStatistics.getPrintVo(), flag));
                    PicDto smallPicDto = new PicDto();
                    ToolsKit.Bean.copyProperties(picDto, smallPicDto);

                    smallPicDto.setPic(picDto.getPic());
                    if (picDto.getHeight() >= Constant.OSS_MAX_VALUE || picDto.getWidth() >= Constant.OSS_MAX_VALUE) {
                        smallPicDto.setPic(picDto.getPic());
                    } else {
                        smallPicDto.setPic(picDto.getPic() + Constant.FEED_SUFFIX);
                    }
                    picDtoList.add(picDto);
                    smallPicDtoList.add(smallPicDto);
                }
            }

            dto.setPicDto(picDtoList);
            dto.setSmallPicDto(smallPicDtoList);

            dto.setLabelMap(this.getLabelMap(feedNote.getLabelIds()));
            this.fillStatisticsNum(feedStatistics, dto);

            //造假行动
            this.forgeStatisticsNum(dto, feedNote);

            dto.setContent(feedNote.getContent());
            dto.setTitle(feedNote.getTitle());
            dto.setFeedId(feedNote.getId());
            dto.setCreateTime(feedNote.getCreatetime());

            if (ToolsKit.isNotEmpty(feedNote.getHtmlUrl())) {
                dto.setHtmlUrl(feedNote.getHtmlUrl());
            } else {
                dto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.FEED_INFO_URL + "?feedid=" + feedNote.getId());
            }
            dto.setShareUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.SHAREVIEW_FEED_INFO_URL + "?feedid=" + feedNote.getId());


            // 此处，发送兴趣圈的话，subject和gradeLevel都不会有值,处理发学习圈的
            if (ToolsKit.isNotEmpty(feedNote.getLabelIds()) && feedNote.getLabelIds().contains(studyLabelId)) {
                dto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.STUDY_NOTE_INFO_URL + "?feedid=" + feedNote.getId());
                dto.setShareUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.SHAREVIEW_STUDY_NOTE_INFO_URL + "?feedid=" + feedNote.getId());
            }

            if (feedNote.getCreateuserid().equals(userId)) {
                dto.setIsFriend(FriendsStatusTypeEnums.ALL_FRIEND.getValue());
            } else if (isMe) {
                dto.setIsFriend(FriendsStatusTypeEnums.NO_FRIEND.getValue());
            } else {
                try {
                    FriendStatusDto friendStatusDto = friendsService.getFriendStatus(userId, feedNote.getCreateuserid());
                    dto.setIsFriend(friendStatusDto.getStatus());
                } catch (Exception e) {
                    e.printStackTrace();
                    dto.setIsFriend(FriendsStatusTypeEnums.NO_FRIEND.getValue());
                }
            }
            //添加关注状态
            if (ToolsKit.isNotEmpty(userId)){
                //增加是否已点赞
                if (giveLikeFeedBuService.hasGiveLikeFeed(feedNote.getId(), userId)){
                    dto.setHadGivedLike(1);
                } else {
                    dto.setHadGivedLike(0);
                }
                dto.setIsFollow(friendsFollowService.getFollowStatus(userId, feedNote.getCreateuserid()));
            }
            if (feedNote.getStickNum()!= FeedNoteStickEnums.NONE.getKey()){
                Map<String, Object> map = new HashMap<>();
                map.put("key", feedNote.getStickNum());
                map.put("name", FeedNoteStickEnums.getMap().get(feedNote.getStickNum()).getDesc());
                map.put("icon", ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), FeedNoteStickEnums.getMap().get(feedNote.getStickNum()).getIcon()));
                dto.setStickObj(map);
            } else {
                dto.setStickObj(new HashMap<>());
            }
            return dto;
        }
        return null;
    }

    public void forgeStatisticsNum(FeedInfoDto dto, FeedNote feedNote) {
        // 造假行动在记录新建半小时后开始
        if (new Date().getTime() - feedNote.getCreatetime().getTime()>1000*60*30){
            if (dto.getDownLoadNum()<5){
                // 下载数量小于5时
                List<PicVo> pics = feedNote.getPic();
                if (ToolsKit.isNotEmpty(pics)){
                    for (PicVo pic : pics) {
                        int times = (int)(Math.random()*5) + 1;
                        for(int i=0; i<times; i++){
                            feedStatisticsBuService.operateNum(feedNote.getUserId(), feedNote.getId(), FeedStatisticsTypeEnums.DOWNLOAD.getValue(), true, pic.getPic());
                        }
                        feedStatisticsBuService.operateNum(feedNote.getId(), times,0,0,0);
                    }
                }
            }
            if (dto.getPrintNum()<5){
                // 打印数量小于5时，因为针对每张图的，所以此处做每张图打印不同
                List<PicVo> pics = feedNote.getPic();
                if (ToolsKit.isNotEmpty(pics)){
                    for (PicVo pic : pics) {
                        int times = (int)(Math.random()*5) + 1;
                        for(int i=0; i<times; i++){
                            feedStatisticsBuService.operateNum(feedNote.getUserId(), feedNote.getId(), FeedStatisticsTypeEnums.PRINT.getValue(), true, pic.getPic());
                        }
                        feedStatisticsBuService.operateNum(feedNote.getId(), 0,times,0,0);
                    }
                }
            }
            if (dto.getShareNum()<30){
                // 分享数量小于30时
                List<PicVo> pics = feedNote.getPic();
                if (ToolsKit.isNotEmpty(pics)){
                    int times = (int)(Math.random()*25) + 5;
                    for(int i=0; i<times; i++){
                        feedStatisticsBuService.operateNum(feedNote.getUserId(), feedNote.getId(), FeedStatisticsTypeEnums.SHARE.getValue(), true, pics.get(0).getPic());
                    }
                    feedStatisticsBuService.operateNum(feedNote.getId(), 0,0,times,0);
                }
            }
        }
    }

    /**
     * 获取动态信息dto
     * 
     * @param userId
     *            用户ID
     * @param id
     *            动态ID
     * @return
     * @throws ServiceException
     */
    public FeedInfoDto getFeedInfoDto(String userId, String id, boolean isMe, boolean isGetReply, List<String> shieldUserIds) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        FeedNote feedNote = this.getFeedNoteById(id);
        if (ToolsKit.isNotEmpty(feedNote) && !shieldUserIds.contains(feedNote.getCreateuserid())) {
            UserLoginDto userLoginDto = userAccountService.getUserInfo(feedNote.getCreateuserid());
            FeedInfoDto feedInfoDto = this.getFeedInfoDto(userId, userLoginDto, feedNote, isMe);
            if (feedInfoDto.getCommentNum() <= 0) {
                feedInfoDto.setFeedCommentDto(new ArrayList<FeedCommentDto>());
            } else {
                feedInfoDto.setFeedCommentDto(feedCommentBuService.getCommentList(feedInfoDto.getFeedId(), 1, 20, null, isGetReply));
            }
            return feedInfoDto;
        }
        return null;
    }

    /**
     * 填充用户信息
     * 
     * @param userId
     *            用户ID
     * @param dto
     */
    public UserLoginDto fillUserInfo(String userId, FeedInfoDto dto) {
        UserLoginDto userLoginDto = userAccountService.getUserInfo(userId);
        if (ToolsKit.isNotEmpty(userLoginDto) && ToolsKit.isNotEmpty(userLoginDto.getUserInfoDto())) {
            dto.setUserId(userId);
            dto.setUserName(userLoginDto.getUserInfoDto().getNickName());
            dto.setUserPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), userLoginDto.getUserInfoDto().getUserPic()));
            dto.setSex(userLoginDto.getUserInfoDto().getSex());
            dto.setIsOfficial(userLoginDto.getUserInfoDto().getIsOfficial());
            dto.setUserTitleObj(userLoginDto.getUserInfoDto().getUserTitleObj());
        }
        return userLoginDto;
    }

    /**
     * 填充统计数
     * 
     * @param feedStatistics
     * @param dto
     */
    public void fillStatisticsNum(FeedStatistics feedStatistics, FeedInfoDto dto) {
        if (ToolsKit.isNotEmpty(feedStatistics)) {
            dto.setLikeNum(this.getAllStatisticsNum(feedStatistics.getLikeVo()));
            dto.setDownLoadNum(this.getAllStatisticsNum(feedStatistics.getDownLoadVo()));
            dto.setPrintNum(this.getAllStatisticsNum(feedStatistics.getPrintVo()));

            dto.setCommentNum(feedStatistics.getCommentNum());
            dto.setShareNum(feedStatistics.getShareNum());
            dto.setGiveLikeNum(feedStatistics.getGiveLikeNum());

            dto.setForgeDownloadNum(feedStatistics.getForgeDownloadNum());
            dto.setForgeGiveLikeNum(feedStatistics.getForgeGiveLikeNum());
            dto.setForgePrintNum(feedStatistics.getForgePrintNum());
            dto.setForgeShareNum(feedStatistics.getForgeShareNum());
        }
    }

    public int getAllStatisticsNum(Map<String, FeedStatisticsVo> voMap) {
        int num = 0;
        if (ToolsKit.isNotEmpty(voMap)) {
            for (Map.Entry<String, FeedStatisticsVo> entry : voMap.entrySet()) {
                num = num + entry.getValue().getNum();
            }
        }
        return num;
    }

    public int getOneStatisticsNum(Map<String, FeedStatisticsVo> voMap, String flag) {
        int num = 0;
        if (ToolsKit.isNotEmpty(voMap)) {
            for (Map.Entry<String, FeedStatisticsVo> entry : voMap.entrySet()) {
                if (entry.getKey().equals(flag)) {
                    num = entry.getValue().getNum();
                    break;
                }
            }
        }
        return num;
    }

    /**
     * 填充统计数
     * 
     * @param feedStatistics
     * @param dto
     */
    public void fillStatisticsNum(FeedStatistics feedStatistics, PicVo picVo, FeedInfoDto dto) {
//        if (ToolsKit.isNotEmpty(feedStatistics)) {
//            String pic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), picVo.getPic());
//            String flag = SecureUtil.md5(pic);
//            dto.setLikeNum(this.getOneStatisticsNum(feedStatistics.getLikeVo(), flag));
//            dto.setDownLoadNum(this.getOneStatisticsNum(feedStatistics.getDownLoadVo(), flag));
//            dto.setPrintNum(this.getOneStatisticsNum(feedStatistics.getPrintVo(), flag));
//            dto.setCommentNum(feedStatistics.getCommentNum());
//            dto.setShareNum(feedStatistics.getShareNum());
//            dto.setGiveLikeNum(feedStatistics.getGiveLikeNum());
//        }
        if (ToolsKit.isNotEmpty(feedStatistics)) {
            dto.setLikeNum(this.getAllStatisticsNum(feedStatistics.getLikeVo()));
            dto.setDownLoadNum(this.getAllStatisticsNum(feedStatistics.getDownLoadVo()));
            dto.setPrintNum(this.getAllStatisticsNum(feedStatistics.getPrintVo()));
            dto.setCommentNum(feedStatistics.getCommentNum());
            dto.setShareNum(feedStatistics.getShareNum());
            dto.setGiveLikeNum(feedStatistics.getGiveLikeNum());
        }
    }

    /**
     * 获取标签集合
     * 
     * @param labelIds
     *            标签ID集合
     * @return
     */
    public Map<String, String> getLabelMap(List<String> labelIds) {
        Map<String, String> labelMap = new HashMap<String, String>();
        if (ToolsKit.isNotEmpty(labelIds)) {
            for (String labelId : labelIds) {
                Label label = jvmCacheBuService.getLabelByBId(labelId);
                if (ToolsKit.isNotEmpty(label)) {
                    labelMap.put(label.getName().trim(), label.getId());
                }
            }
        }
        return labelMap;
    }

    /**
     * 获取图片dto
     * 
     * @param feedId
     *            动态ID
     * @param userId
     *            用户ID
     * @param picVo
     *            图片地址
     * @return
     */
    public PicDto getPicDto(String feedId, String userId, PicVo picVo) {
        PicDto picDto = new PicDto();
        picDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picVo.getPic()));
        picDto.setHeight(picVo.getHeight());
        picDto.setWidth(picVo.getWidth());
        try {
            if (ToolsKit.isNotEmpty(userId)) {
                LikeFeed likeFeed = likeFeedBuService.getLikeFeedByStatus(feedId, userId, SecureUtil.md5(picVo.getPic()));
                if (ToolsKit.isNotEmpty(likeFeed)) {
                    picDto.setId(likeFeed.getId());
                }
            } else {
                picDto.setId(StringUtils.EMPTY);
            }
        } catch (Exception e) {
        }
        return picDto;
    }

    /**
     * 发布动态
     * 
     * @param userId
     *            用户ID
     * @param dto
     *            发布动态dto
     */
    public void addFeedNote(String userId, AddFeedNoteDto dto) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(dto)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("动态内容不能为空");
        }
        if (ToolsKit.isEmpty(dto.getPic())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("图片不能为空");
        }
        if (ToolsKit.isEmpty(dto.getLabelIds())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标签不能为空");
        }

        // 判断当前用户是否被禁用 2021-02-09 by linnt
        UserLoginDto userLoginDto = userAccountService.getUserInfo(userId);
        if (ToolsKit.isNotEmpty(userLoginDto)
        && ToolsKit.isNotEmpty(userLoginDto.getUserInfoDto())
        && userLoginDto.getUserInfoDto().getForbidden()==1){
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("您的账号已被禁用，详情请联系管理员");
        }

        FeedNote feedNote = new FeedNote();
        ToolsKit.setIdEntityData(feedNote, userId);
        if (ToolsKit.isNotEmpty(dto.getDelayedTime())){
            feedNote.setCreatetime(dto.getDelayedTime());
            feedNote.setUpdatetime(dto.getDelayedTime());
        }
        feedNote.setUserId(userId);
        feedNote.setContent(ToolUtils.replaceHtml(dto.getContent()));
        List<PicVo> picList = new ArrayList<PicVo>();
        for (String pic : dto.getPic()) {
            PicInfoDto picInfoDto = WebKit.getPicInfo(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), pic));
            PicVo picVo = new PicVo();
            String tempPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), pic);
            if (!ToolsKit.URL.isUrl(tempPic) && !tempPic.startsWith("/")) {
                tempPic = "/" + tempPic;
            }
            picVo.setPic(tempPic);
            if (ToolsKit.isNotEmpty(picInfoDto)) {
                picVo.setHeight(picInfoDto.getImageHeight());
                picVo.setWidth(picInfoDto.getImageWidth());
            }
            picList.add(picVo);
        }
        feedNote.setPic(picList);
        feedNote.setLabelIds(dto.getLabelIds());
        feedNote.setHasPic(ToolsKit.isEmpty(dto.getPic()) ? ToolsConst.STATUS_0 : ToolsConst.STATUS_1);
        feedNote.setIsHot(ToolsConst.STATUS_0);
        this.save(feedNote);
        userFeedBuService.addUserFeed(userId, feedNote.getId());
        indexFeedBuService.saveIndexFeed(feedNote.getId(), IndexFeedTypeEnums.NEW.getValue(), feedNote.getCreatetime().getTime());
        if (dto.getIsLabel() == ToolsConst.STATUS_1 || jvmCacheBuService.getWhileMap().containsKey(userId)) {
            for (String labelId : feedNote.getLabelIds()) {
                for (PicVo vo : feedNote.getPic()) {
                    labelFeedBuService.addLabelFeed(feedNote.getId(), labelId, vo.getPic());
                }
            }
            for (PicVo vo : feedNote.getPic()) {
                labelFeedBuService.addLabelFeed(feedNote.getId(), StringUtils.EMPTY, vo.getPic());
            }
        }

        // 机器人自动关注3--7
        ToolsKit.Thread.execute(new Runnable() {
            @Override
            public void run() {
                int times = (int)(Math.random()*4) + 3;
                String listfollowKey = "comm:auto:follow:list";
                long currTime = System.currentTimeMillis();
                for (int i = 0; i < times; i++) {
                    currTime += (long)(Math.random()*1000)*1000;
                    CacheKit.cache().zadd(listfollowKey, currTime, userId+"_"+currTime, ToolsConst.WEEK_SECOND);
                }
            }
        });
    }

    /**
     * 发布动态
     *
     * @param userId
     *            用户ID
     * @param dto
     *            发布动态dto
     */
    public void addFeedNoteStudy(String userId, AddFeedNoteDto dto) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(dto)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("动态内容不能为空");
        }
        if (ToolsKit.isEmpty(dto.getPic())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("图片不能为空");
        }

        if (ToolsKit.isEmpty(dto.getSubject())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("学科不能为空");
        }
        if (ToolsKit.isEmpty(dto.getGradeLevel())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("年级不能为空");
        }

        if (ToolsKit.isEmpty(dto.getTitle())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标题不能为空");
        }

        // 判断当前用户是否被禁用 2021-02-09 by linnt
        UserLoginDto userLoginDto = userAccountService.getUserInfo(userId);
        if (ToolsKit.isNotEmpty(userLoginDto)
                && ToolsKit.isNotEmpty(userLoginDto.getUserInfoDto())
                && userLoginDto.getUserInfoDto().getForbidden()==1){
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("您的账号已被禁用，详情请联系管理员");
        }

        // 标签这里特殊处理
        String studyLabelIds = labelBuService.getLabelListStudy();
        dto.setLabelIds(new ArrayList<>());
        dto.getLabelIds().add(studyLabelIds);
        if (ToolsKit.isEmpty(dto.getLabelIds())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标签不能为空");
        }
        FeedNote feedNote = new FeedNote();
        ToolsKit.setIdEntityData(feedNote, userId);
        // 如果有延时发布时间
        if (ToolsKit.isNotEmpty(dto.getDelayedTime())) {
            feedNote.setCreatetime(dto.getDelayedTime());
            feedNote.setUpdatetime(dto.getDelayedTime());
        }
        feedNote.setUserId(userId);
        feedNote.setContent(ToolUtils.replaceHtml(dto.getContent()));
        feedNote.setGradeLevel(dto.getGradeLevel());
        feedNote.setSubject(dto.getSubject());
        feedNote.setTitle(dto.getTitle());

        UserLoginDto userInfoDto = userAccountService.getUserInfo(userId);
        if (ToolsKit.isNotEmpty(userInfoDto)){
            feedNote.setKeyword(userInfoDto.getUserInfoDto().getNickName()
                            + StudyNoteGradeEnum.getValueByKey(feedNote.getGradeLevel())
                            + StudyNoteSubjectEnum.getValueByKey(feedNote.getSubject())
                            + dto.getTitle()
                            + dto.getContent()
                    );
            if (ToolsKit.isNotEmpty(userInfoDto.getUserInfoDto())
                    && ToolsKit.isNotEmpty(userInfoDto.getUserInfoDto().getGradeLevelObj())
                    && ToolsKit.isNotEmpty(userInfoDto.getUserInfoDto().getGradeLevelObj().get("id"))){
                feedNote.setGradeNum(Integer.parseInt(userInfoDto.getUserInfoDto().getGradeLevelObj().get("id")+""));
            }
        } else {
            feedNote.setKeyword(StudyNoteGradeEnum.getValueByKey(feedNote.getGradeLevel())
                    + StudyNoteSubjectEnum.getValueByKey(feedNote.getSubject())
                    + dto.getTitle()
                    + dto.getContent()
            );
        }

        List<PicVo> picList = new ArrayList<PicVo>();
        for (String pic : dto.getPic()) {
            PicInfoDto picInfoDto = WebKit.getPicInfo(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), pic));
            PicVo picVo = new PicVo();
            String tempPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), pic);
            if (!ToolsKit.URL.isUrl(tempPic) && !tempPic.startsWith("/")) {
                tempPic = "/" + tempPic;
            }
            picVo.setPic(tempPic);
            if (ToolsKit.isNotEmpty(picInfoDto)) {
                picVo.setHeight(picInfoDto.getImageHeight());
                picVo.setWidth(picInfoDto.getImageWidth());
            }
            picList.add(picVo);
        }
        feedNote.setPic(picList);
        feedNote.setLabelIds(dto.getLabelIds());
        feedNote.setHasPic(ToolsKit.isEmpty(dto.getPic()) ? ToolsConst.STATUS_0 : ToolsConst.STATUS_1);
        feedNote.setIsHot(ToolsConst.STATUS_1);
        this.save(feedNote);
        userFeedBuService.addUserFeed(userId, feedNote.getId());
        // 一开始保存成hot，这样查找“最新”时就不会显示了
        indexFeedBuService.saveIndexFeed(feedNote.getId(), IndexFeedTypeEnums.HOT.getValue(), feedNote.getCreatetime().getTime());

        // “学习”标签特殊处理，全加进去
        for (String labelId : feedNote.getLabelIds()) {
            for (PicVo vo : feedNote.getPic()) {
                labelFeedBuService.addLabelFeed(feedNote.getId(), labelId, vo.getPic());
            }
        }
        for (PicVo vo : feedNote.getPic()) {
            labelFeedBuService.addLabelFeed(feedNote.getId(), StringUtils.EMPTY, vo.getPic());
        }

        // 机器人自动关注3--7
        ToolsKit.Thread.execute(new Runnable() {
            @Override
            public void run() {
                int times = (int)(Math.random()*4) + 3;
                String listfollowKey = "comm:auto:follow:list";
                long currTime = System.currentTimeMillis();
                for (int i = 0; i < times; i++) {
                    currTime += (long)(Math.random()*1000)*1000;
                    CacheKit.cache().zadd(listfollowKey, currTime, userId+"_"+currTime, ToolsConst.WEEK_SECOND);
                }
            }
        });
    }

    /**
     * 获取动态详情
     * 
     * @param userId
     *            用户ID
     * @param feedId
     *            动态ID
     * @throws ServiceException
     */
    public FeedInfoDto getFeedNote(String userId, String feedId) throws ServiceException {
        // if (ToolsKit.isEmpty(userId)) {
        // throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        // }
        if (ToolsKit.isEmpty(feedId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("动态ID不能为空");
        }
        FeedInfoDto feedInfoDto = this.getFeedInfoDto(userId, feedId, false, true, new ArrayList<String>());
        if (ToolsKit.isEmpty(feedInfoDto)) {
            throw new ServiceException().setCode(ExceptionEnums.RECORD_IS_NOT_FOUND.getCode()).setMessage("该动态不存在或已被删除");
        }
        return feedInfoDto;
    }

    /**
     * 删除动态信息
     * 
     * @param feedId
     *            动态ID
     * @throws ServiceException
     */
    public void delFeedNote(String feedId) throws ServiceException {
        if (ToolsKit.isEmpty(feedId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("动态ID不能为空");
        }
        FeedNote feedNote = this.getFeedNoteById(feedId);
        if (ToolsKit.isEmpty(feedNote)) {
            throw new ServiceException().setCode(ExceptionEnums.RECORD_IS_NOT_FOUND.getCode()).setMessage("该动态不存在或已被删除");
        }
        FeedInfoDto feedInfoDto = this.getFeedNote(feedNote.getUserId(), feedId);

        feedNote.setStatus(ToolsConst.DATA_DELETE_STATUS);
        feedNoteDao.saveEntity(feedNote);
        feedNoteCacheService.delFeedNote(feedId);
        feedCommentBuService.delFeedCommentByFeedId(feedId);
        feedStatisticsBuService.delFeedStatistics(feedId);
        indexFeedBuService.delIndexFeed(feedId);
        userFeedBuService.delUserFeed(feedNote.getCreateuserid(), feedId);
        labelFeedBuService.delLabelFeedByFeedId(feedId);

        // 删帖的话，触发删除积分操作
        // 删除自动点赞和自动加粉的数据
        try {
            String studyLabelId = labelBuService.getLabelListStudy();
            if (ToolsKit.isNotEmpty(feedNote.getLabelIds()) && feedNote.getLabelIds().contains(studyLabelId)) {
                coinService.cancelMission(feedNote.getUserId(), MissionCodeEnums.ADDSTUDYNOTE.getCode(), feedInfoDto.getCreateTime().getTime());
            } else {
                coinService.cancelMission(feedNote.getUserId(), MissionCodeEnums.ADDFEEDNOTE.getCode(), feedInfoDto.getCreateTime().getTime());
            }
            String keyUser = "comm:auto:follow:list";
            List<String> userIdsList = CacheKit.cache().zrevrank(keyUser);
            if (ToolsKit.isNotEmpty(userIdsList)){
                for (String ids: userIdsList) {
                    String userId = ids.split("_")[0];
                    if (userId.equals(feedNote.getUserId())){
                        // 删除列表中的这个值
                        CacheKit.cache().zrem(keyUser, ids);
                    }
                }
            }

            String autoGiveLikeIdsKey = "comm:auto:givelike:list";
            List<String> strList = CacheKit.cache().zrevrank(autoGiveLikeIdsKey);
            if (ToolsKit.isNotEmpty(strList)){
                for (String str: strList) {
                    String [] feedIds = str.split("_");
                    if (feedId.equals(feedIds[0])){
                        // 删除列表中的这个值
                        CacheKit.cache().zrem(autoGiveLikeIdsKey, str);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新动态是否热门状态
     * 
     * @param feedId
     *            动态ID
     * @param isHot
     *            是否热门
     * @throws ServiceException
     */
    public void updateHotStatus(String feedId, int isHot) throws ServiceException {
        if (ToolsKit.isEmpty(feedId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("动态ID不能为空");
        }
        FeedNote feedNote = this.getFeedNoteById(feedId);
        if (ToolsKit.isEmpty(feedNote)) {
            throw new ServiceException().setCode(ExceptionEnums.RECORD_IS_NOT_FOUND.getCode()).setMessage("该动态不存在或已被删除");
        }
        if (feedNote.getIsHot() != isHot) {
            feedNote.setIsHot(isHot);
            this.save(feedNote);
        }
    }

    public List<FeedNote> findListByLabelId(String labelId){
        return feedNoteDao.findFeedNoteIdsList(null, null, null, null, null, labelId, null, null, null, null);
    }

    public List<FeedNote> findListByLabelId(String labelId, String stickNum, String subject, String gradeLevel, String keyword){
        return feedNoteDao.findFeedNoteIdsList(null, null, null, null, null, labelId, stickNum, subject, gradeLevel, keyword);
    }

    public Page<FeedNote> findListPageByLabelId(int pageNo, int pageSize, String labelId, String stickNum, String subject, String gradeLevel, String keyword){
        return feedNoteDao.findFeedNoteIdsPage(pageNo, pageSize, null, null, null, labelId, stickNum, subject, gradeLevel, keyword);
    }

    /***
     * 自动点赞
     *      ①获取一天之类发布的动态数据
     *      ②对发布超过半小时的动态处理(取消掉此步骤)
     *      ③判断是否已对该记录进行计划时间点赞
     *      ④没有话，判断图片多少，产生随机的点赞数
     *      ⑤标记该记录已进行计划时间点赞
     *      ⑥完
     */
    public void autoGiveLike(){
        String listKey = "comm:auto:givelike:list";
        Set<String> list = CacheKit.cache().zrangebyscore(listKey, 0,System.currentTimeMillis());
        if (ToolsKit.isNotEmpty(list)) {
            for (String str : list) {
                String [] feedIds = str.split("_");
                try {
                    UserLoginDto userLoginDto = userAccountService.getRobotUserInfoRandom();
                    giveLikeFeedBuService.addGiveLikeFeed(userLoginDto.getUserInfoDto().getUserId(), feedIds[0]);
                    feedStatisticsBuService.operateNum(feedIds[0], 0,0,0,1);
                } catch (Exception e){

                }
                CacheKit.cache().zrem(listKey, str);
            }
        }

        // 处理自动关注的
        String listfollowKey = "comm:auto:follow:list";
        Set<String> listfollow = CacheKit.cache().zrangebyscore(listfollowKey, 0,System.currentTimeMillis());
        if (ToolsKit.isNotEmpty(listfollow)) {
            for (String str : listfollow) {
                String [] userId = str.split("_");
                try {
                    ToolsKit.Thread.execute(new Runnable() {
                        @Override
                        public void run() {
                            sleep(Math.random()*60*1000);
                            UserLoginDto userLoginDto = userAccountService.getRobotUserInfoRandom();
                            friendsFollowService.addFriendsFollow(userLoginDto.getUserInfoDto().getUserId(), userId[0]);
                        }
                    });
                } catch (Exception e){

                }
                CacheKit.cache().zrem(listfollowKey, str);
            }
        }
    }

    public void autoAddGiveLikeList(){
        Date endDate = new Date();
        Date beginDate = ToolsKit.Date.yesterday();
        List<FeedNote> feedNotes = feedNoteDao.findFeedNoteIdsList(null, null, beginDate, endDate, null,null,null,null,null,null);
        String preKey = "comm:auto:givelike:new:feedid:";
        String listKey = "comm:auto:givelike:list";
        if (ToolsKit.isNotEmpty(feedNotes)){
            for (FeedNote feedNote: feedNotes) {
                // 判断是否已经进行过点赞了
                String key = preKey + feedNote.getId();
                if (!CacheKit.cache().exists(key)){
                    FeedNote temp = getFeedNoteById(feedNote.getId());
                    // 不存在，则进行处理
                    List<PicVo> pics = temp.getPic();
                    if (ToolsKit.isNotEmpty(pics)){
                        int times = 0;
                        if (pics.size()>=9){
                            times = (int)(Math.random()*15)+10;
                        } else if (pics.size()>=5){
                            times = (int)(Math.random()*10)+10;
                        } else {
                            times = (int)(Math.random()*7)+3;
                        }
                        long thisTime = System.currentTimeMillis();
                        for (int i = 0; i < times; i++) {
                            long sleepTimes = (long)(Math.random()*2000)*1000;
                            thisTime += sleepTimes;
                            CacheKit.cache().zadd(listKey, thisTime, feedNote.getId()+"_"+thisTime, ToolsConst.WEEK_SECOND);
                        }
                    }
                    // 一系列处理完后，设置key
                    CacheKit.cache().set(key, feedNote.getId(), ToolsConst.DAY_SECOND*2);
                }
            }
        }
    }
}
