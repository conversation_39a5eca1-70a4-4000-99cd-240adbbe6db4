package net.xplife.system.shitiku.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
@Data
public class TranslationPrinterHistoryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    private String userId;

    private List<TranslationPrintHistoryDataVO> printData;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
