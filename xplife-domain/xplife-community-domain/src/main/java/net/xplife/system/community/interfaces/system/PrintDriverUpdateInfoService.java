package net.xplife.system.community.interfaces.system;

import net.xplife.system.account.dto.user.UserLoginDto;
import net.xplife.system.community.entity.system.PrintDriverUpdateInfo;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/5/31 8:52
 * @description：
 * @modified By：
 * @version: $
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface PrintDriverUpdateInfoService {

    @RequestMapping(value = "/community/v1/printdriver/delete", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void deleteById(@RequestParam("id") String id);

    @RequestMapping(value = "/community/v1/printdriver/saveorupdate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public PrintDriverUpdateInfo addOrUpdateItem(@RequestBody PrintDriverUpdateInfo item);

    @RequestMapping(value = "/community/v1/printdriver/getPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<PrintDriverUpdateInfo> findPage(@RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize, @RequestParam("printerModel") String printerModel);

    @RequestMapping(value = "/community/v1/printdriver/findById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    PrintDriverUpdateInfo getById(@RequestParam("id") String id);

    @RequestMapping(value = "/community/v1/printdriver/findListByPrinterType", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<PrintDriverUpdateInfo> findListByPrinterType(@RequestParam("printerType") String printerType);

    @RequestMapping(value = "/community/v1/printdriver/findWhileList", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<UserLoginDto> findWhileList();

    @RequestMapping(value = "/community/v1/printdriver/removeWhileList", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String removeWhileUser(@RequestParam("otherid") String otherid);

    @RequestMapping(value = "/community/v1/printdriver/addWhileList", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String addWhileUser(@RequestParam("otherid") String otherid);

    @RequestMapping(value = "/community/v1/printdriver/updatePass", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String updatePass(@RequestParam("id") String id);

    @RequestMapping(value = "/community/v1/printdriver/updateReject", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String updateReject(@RequestParam("id") String id);

    @RequestMapping(value = "/community/v1/printdriver/updateSubmit", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String updateSubmit(@RequestParam("id") String id);


}
