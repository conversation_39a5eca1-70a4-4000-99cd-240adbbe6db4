package net.xplife.system.shitiku.service;

import net.xplife.system.mongo.common.Page;
import net.xplife.system.shitiku.dao.TranslationPrintHistoryDao;
import net.xplife.system.shitiku.dto.*;
import net.xplife.system.shitiku.entity.TranslationDrafts;
import net.xplife.system.shitiku.entity.TranslationPrintHistory;
import net.xplife.system.shitiku.enums.ConversationStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
@Service
public class TranslationPrintHistoryService {
    private static final Logger logger = LoggerFactory.getLogger(TranslationPrintHistoryService.class);

    @Autowired
    private TranslationPrintHistoryDao translationPrintHistoryDao;

    @Autowired
    private MongoTemplate mongoTemplate;

    public TranslationPrintHistory saveTranslationPrintHistory(TranslationPrintHistoryDTO dto) {
        logger.info("保存文本翻译打印历史: {}", dto);

        TranslationPrintHistory conversation = new TranslationPrintHistory();
        conversation.setUserId(dto.getUserId());
        conversation.setPrintData(dto.getPrintData());
        conversation.setCreateTime(new Date());
        conversation.setUpdateTime(new Date());
        conversation.setStatus(ConversationStatus.NORMAL.getCode()); // 使用枚举替代魔法值

        TranslationPrintHistory savedConversation = translationPrintHistoryDao.save(conversation);
        logger.info("保存文本翻译打印历史成功，ID: {}", savedConversation.getId());

        return savedConversation;
    }

    public Page<TranslationPrintHistory> findTranslationDrafts(TranslationPrintHistoryQueryDTO queryDTO) {
        logger.info("查询文本翻译打印历史列表: {}", queryDTO);

        // 构建分页参数
        PageRequest pageRequest = PageRequest.of(queryDTO.getPageNum() - 1, queryDTO.getPageSize(), Sort.by(Sort.Direction.DESC, "updateTime"));

        // 构建查询条件
        Query query = buildQuery(queryDTO);
        query.with(pageRequest);

        // 执行查询并获取总数
        List<TranslationPrintHistory> content = mongoTemplate.find(query, TranslationPrintHistory.class);
        long totalCount = mongoTemplate.count(query.skip(-1).limit(-1), TranslationPrintHistory.class);

        // 构建分页结果
        Page<TranslationPrintHistory> customPage = new Page<>();
        customPage.setPageNo(queryDTO.getPageNum());
        customPage.setPageSize(queryDTO.getPageSize());
        customPage.setTotalCount(totalCount);
        customPage.setResult(content);

        logger.info("查询文本翻译打印历史列表成功，结果数量: {}", totalCount);
        return customPage;
    }

    public void batchDeleteTranslationPrintHistory(TranslationPrintHistoryBatchDeleteDTO batchDeleteDTO) {
        logger.info("批量删除文本翻译对话: {}", batchDeleteDTO);

        List<String> ids = batchDeleteDTO.getIds();
        if (ids == null || ids.isEmpty()) {
            logger.warn("批量删除文本翻译打印历史失败，ID列表为空");
            return;
        }

        // 使用MongoTemplate执行批量更新，将状态设置为已删除
        Query query = new Query(Criteria.where("id").in(ids));
        Update update = new Update()
                .set("status", ConversationStatus.DELETED.getCode()) // 使用枚举替代魔法值
                .set("updateTime", new Date());

        long count = mongoTemplate.updateMulti(query, update, TranslationPrintHistory.class).getModifiedCount();
        logger.info("批量删除文本翻译打印历史成功，删除数量: {}", count);
    }

    /**
     * 构建查询条件
     */
    private Query buildQuery(TranslationPrintHistoryQueryDTO queryDTO) {
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(ConversationStatus.NORMAL.getCode()));

        if (StringUtils.hasText(queryDTO.getUserId())) {
            query.addCriteria(Criteria.where("userId").is(queryDTO.getUserId()));
        }

//        if (StringUtils.hasText(queryDTO.getContext())) {
//            // 创建两个独立的条件
//            Criteria originalCriteria = Criteria.where("original").regex(queryDTO.getContext(), "i");
//            Criteria translationCriteria = Criteria.where("translation").regex(queryDTO.getContext(), "i");
//
//            // 将两个条件用"或"逻辑组合
//            query.addCriteria(new Criteria().orOperator(originalCriteria, translationCriteria));
//        }

        return query;
    }
}
