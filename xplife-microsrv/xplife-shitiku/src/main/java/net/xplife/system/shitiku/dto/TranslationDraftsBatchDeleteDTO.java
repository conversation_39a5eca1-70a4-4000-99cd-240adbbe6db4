package net.xplife.system.shitiku.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description:
 */
@Data
public class TranslationDraftsBatchDeleteDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要删除的对话ID列表
     */
    @NotEmpty(message = "对话ID列表不能为空")
    private List<String> ids;
}
