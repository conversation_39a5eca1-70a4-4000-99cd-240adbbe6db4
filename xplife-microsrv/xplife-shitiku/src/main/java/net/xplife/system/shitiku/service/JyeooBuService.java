package net.xplife.system.shitiku.service;

import cn.hutool.core.util.ImageUtil;
import com.alibaba.fastjson.JSONObject;
import net.xplife.system.cache.common.CacheCommonTools;
import net.xplife.system.http.core.response.HttpResponse;
import net.xplife.system.http.kit.HttpKit;
import net.xplife.system.shitiku.dto.SearchObject;
import net.xplife.system.shitiku.enums.JyeooApiEnums;
import net.xplife.system.shitiku.utils.Constant;
import net.xplife.system.shitiku.utils.DES3;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.JsonKit;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.activation.MimetypesFileTypeMap;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class JyeooBuService {
    @Autowired
    private CacheCommonTools cacheCommonTools;

    @Autowired
    private JyeooUserReqCountBuService jyeooUserReqCountBuService;

    @Autowired
    private JyeooQuesAnalysisBuService jyeooQuesAnalysisBuService;

    /**
     * 新用户注册，同一用户多次调用只会修改该用户的相关信息，例如用户密码、用户性别、用户角色、用户学校等。
     */
    public String register(String school, String userId, String userPwd, String userName, String userRole, String userSex) throws ServiceException {
        if (ToolsKit.isEmpty(school)) {
            throw new ServiceException("用户学校不能为空");
        }
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户标识不能为空");
        }
        if (ToolsKit.isEmpty(userPwd)) {
            throw new ServiceException("用户密码不能为空");
        }
        if (ToolsKit.isEmpty(userName)) {
            throw new ServiceException("用户名称不能为空");
        }
        if (ToolsKit.isEmpty(userRole)) {
            throw new ServiceException("用户角色不能为空");
        }
        if (ToolsKit.isEmpty(userSex)) {
            throw new ServiceException("用户性别不能为空");
        }
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("ApiID", Constant.JYEOO_API_ID);
        paramMap.put("ApiPwd", Constant.JYEOO_API_PWD);
        paramMap.put("School", school);
        paramMap.put("UserID", userId);
        paramMap.put("UserPwd", userPwd);
        paramMap.put("UserName", userName);
        paramMap.put("UserRole", userRole);
        paramMap.put("UserSex", userSex);
        String json = JsonKit.toJsonString(paramMap);
        String eson = null;
        try {
            eson = DES3.encrypt(json, Constant.JYEOO_API_KEY);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String url = String.format("http://api.jyeoo.com/v1/register?id=%s&v=%s", Constant.JYEOO_API_ID, eson);
        HttpResponse httpResponse = HttpKit.http().url(url).param("123", "123").post();
        if (httpResponse.isSuccess()) {
            return httpResponse.asString().replace("\"", "");
        }
        throw new ServiceException("请求失败");
    }

    /**
     * 用户登录并获取登录凭证（有效期1周）
     */
    public String getToken(String userId, String userPwd) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户标识不能为空");
        }
        if (ToolsKit.isEmpty(userPwd)) {
            throw new ServiceException("用户密码不能为空");
        }
        String key = "jyeoo:token:id:" + userId;
        String token = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isEmpty(token)) {
            String s = String.format("%s#@@#%s#@@#%s", Constant.JYEOO_API_ID, userId, userPwd);
            String v = null;
            try {
                v = DES3.encrypt(s, Constant.JYEOO_API_KEY);
            } catch (Exception e) {
                e.printStackTrace();
            }
            boolean isSuccess = false;
            int i = 0;
            while (i < 2 && !isSuccess) {
                String url = String.format("http://api.jyeoo.com/v1/user?id=%s&v=%s", Constant.JYEOO_API_ID, v);
                HttpResponse httpResponse = HttpKit.http().url(url).param("123", "123").post();
                if (httpResponse.isSuccess()) {
                    token = httpResponse.asString();
                    cacheCommonTools.saveCacheValue(key, token, ToolsConst.DAY_SECOND * 6);
                    isSuccess = true;
                }
                i++;
            }
        }
        return token;
    }

    /**
     * 获取学科信息，用于创建学科枚举
     * 
     * @param userId
     * @return
     * @throws ServiceException
     */
    public String getSubjects(String userId) throws ServiceException {
        String key = "jyeoo:subjects:id:" + userId;
        String value = cacheCommonTools.getCacheValue(key, String.class);
        System.out.println("");
        value = null;
        if (ToolsKit.isEmpty(value)) {
            String url = "http://api.jyeoo.com/v1/subject";
            String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
            Map<String, String> headerMap = getHeaderMap(token);
            HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
            if (httpResponse.isSuccess()) {
                value = httpResponse.asString();
                if (ToolsKit.isNotEmpty(value)) {
//                    cacheCommonTools.saveCacheValue(key, value, ToolsConst.THIRTY_MINUTES);
                    cacheCommonTools.saveCacheValue(key, value, ToolsConst.MONTH_SECOND);   //改成每个月刷新一次
                } else {
                    throw new ServiceException("请求失败");
                }
            }
        }
        return value;
    }

    /**
     * 获取地区信息
     * 
     * @param userId
     * @return
     * @throws ServiceException
     */
    public String getRegions(String userId) throws ServiceException {
        String url = "http://api.jyeoo.com/v1/region";
        String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
        Map<String, String> headerMap = getHeaderMap(token);
        HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
        if (httpResponse.isSuccess()) {
            return httpResponse.asString();
        }
        throw new ServiceException("请求失败");
    }

    /**
     * 获取学科考点
     * 
     * @param userId
     * @return
     * @throws ServiceException
     */
    public String getPoints(String userId, String subject) throws ServiceException {
        String key = "jyeoo:points:id:" + userId + ":" + subject;
        String value = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isEmpty(value)) {
            String url = String.format("http://api.jyeoo.com/v1/%s/point2", subject);
            String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
            Map<String, String> headerMap = getHeaderMap(token);
            HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
            if (httpResponse.isSuccess()) {
                value = httpResponse.asString();
                if (ToolsKit.isNotEmpty(value)) {
                    cacheCommonTools.saveCacheValue(key, value, ToolsConst.THIRTY_MINUTES);
                } else {
                    throw new ServiceException("请求失败");
                }
            }
        }
        return value;
    }

    /**
     * 获取年级信息
     * 
     * @param userId
     * @return
     * @throws ServiceException
     */
    public String getGrades(String userId) throws ServiceException {
        String url = "http://api.jyeoo.com/v1/grade";
        String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
        Map<String, String> headerMap = getHeaderMap(token);
        HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
        if (httpResponse.isSuccess()) {
            return httpResponse.asString();
        }
        throw new ServiceException("请求失败");
    }

    /**
     * 获取试题题型
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @return
     * @throws ServiceException
     */
    public String getCates(String userId, String subject) throws ServiceException {
        String key = "jyeoo:cates:id:" + userId + ":" + subject;
        String value = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isEmpty(value)) {
            String url = String.format("http://api.jyeoo.com/v1/%s/common?tp=1", subject);
            String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
            Map<String, String> headerMap = getHeaderMap(token);
            HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
            if (httpResponse.isSuccess()) {
                value = httpResponse.asString();
                if (ToolsKit.isNotEmpty(value)) {
                    cacheCommonTools.saveCacheValue(key, value, ToolsConst.WEEK_SECOND);
                } else {
                    throw new ServiceException("请求失败");
                }
            }
        }
        return value;
    }

    /**
     * 获取试题来源
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @return
     * @throws ServiceException
     */
    public String getSources(String userId, String subject) throws ServiceException {
        String key = "jyeoo:sources:id:" + userId + ":" + subject;
        String value = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isEmpty(value)) {
            String url = String.format("http://api.jyeoo.com/v1/%s/common?tp=2", subject);
            String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
            Map<String, String> headerMap = getHeaderMap(token);
            HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
            if (httpResponse.isSuccess()) {
                value = httpResponse.asString();
                if (ToolsKit.isNotEmpty(value)) {
                    cacheCommonTools.saveCacheValue(key, value, ToolsConst.WEEK_SECOND);
                } else {
                    throw new ServiceException("请求失败");
                }
            }
        }
        return value;
    }

    public String getArea(String userId) {
        String key = "jyeoo:areas:all";
        String value = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isEmpty(value)) {
            String url = "http://api.jyeoo.com/v1/region";
            String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
            Map<String, String> headerMap = getHeaderMap(token);
            HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
            if (httpResponse.isSuccess()) {
                value = httpResponse.asString();
                if (ToolsKit.isNotEmpty(value)) {
                    cacheCommonTools.saveCacheValue(key, value, ToolsConst.WEEK_SECOND);
                } else {
                    throw new ServiceException("请求失败");
                }
            }
        }
        return value;
    }

    /**
     * 获取教材版本
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @return
     * @throws ServiceException
     */
    public String getEditions(String userId, String subject) throws ServiceException {
        String key = "jyeoo:editions:id:" + userId + ":" + subject;
        String value = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isEmpty(value)) {
            String url = String.format("http://api.jyeoo.com/v1/%s/common?tp=3", subject);
            String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
            Map<String, String> headerMap = getHeaderMap(token);
            HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
            if (httpResponse.isSuccess()) {
                value = httpResponse.asString();
                if (ToolsKit.isNotEmpty(value)) {
                    cacheCommonTools.saveCacheValue(key, value, ToolsConst.DAY_SECOND);
                } else {
                    throw new ServiceException("请求失败");
                }
            }
        }
        return value;
    }

    /**
     * 获取所选学科的教材（包含教材章节）
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @return
     * @throws ServiceException
     */
    public String getBooks(String userId, String subject) throws ServiceException {
        String key = "jyeoo:book:uid:" + userId + ":" + subject;
//        String result = cacheCommonTools.getCacheValue(key, String.class);
        String result = null;
        if (ToolsKit.isEmpty(result)) {
            String url = String.format("http://api.jyeoo.com/v1/%s/book2", subject);
            String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
            Map<String, String> headerMap = getHeaderMap(token);
            System.out.println("----------------------------------------");
            System.out.println("请求url：" + url);
            System.out.println("请求toke" + headerMap);
            HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
            if (httpResponse.isSuccess()) {
                result = httpResponse.asString();
//                System.out.println("返回结果：" + result);
                if (ToolsKit.isNotEmpty(result)) {
//                    cacheCommonTools.saveCacheValue(key, result, ToolsConst.DAY_SECOND);
                } else {
                    throw new ServiceException("请求失败");
                }
            }
            System.out.println("----------------------------------------");
        }
//        System.out.println("------------->>>>>>>>>>>>>getBooks");
//        System.out.println(result);
        return result;
    }

    /**
     * 获取试题解析
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @param id
     *            题目ID
     * @return
     * @throws ServiceException
     */
    public String getQues(String userId, String subject, String id) throws ServiceException {
        String key = "jyeoo:ques:id:" + subject + ":" + id;
        String result = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isEmpty(result)) {
            // 查看当前用户是否请求该接口达到上限
            this.checkAndSaveReqParms(JyeooApiEnums.VIEW_QUES.getKey(), userId);

            String url = String.format("http://api.jyeoo.com/v1/%s/counter/QuesGet?id=%s", subject, id);
//            String url = String.format("http://api.jyeoo.com/v1/%s/ques/%s", subject, id);
            String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
            Map<String, String> headerMap = getHeaderMap(token);
            HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
            if (httpResponse.isSuccess()) {
                result = httpResponse.asString();
                if (ToolsKit.isNotEmpty(result)) {
                    cacheCommonTools.saveCacheValue(key, result, ToolsConst.DAY_SECOND);
                } else {
                    throw new ServiceException("请求失败");
                }
            }
            ToolsKit.Thread.execute(new Runnable() {
                @Override
                public void run() {
                    // 作为统计用
                    jyeooQuesAnalysisBuService.saveDetial(id, subject, userId);
                }
            });

        }
//        System.out.println("------------->>>>>>>>>>>>>getQues");
//        System.out.println(result);
        return result;
    }

    /**
     * 批量获取试题题干
     * 
     * @param userId
     * @param subject
     *            学科英文名称
     * @param ids
     *            试题ID集合
     * @return
     * @throws ServiceException
     */
    public String getBatchQueses(String userId, String subject, List<String> ids) throws ServiceException {

//        String sids = JsonKit.toJsonString(ids);
        String sids = ids.stream().map(String::valueOf).collect(Collectors.joining(","));
        String url = null;
        try {
            System.out.println("sids=" + sids);
//            url = String.format("http://api.jyeoo.com/v1/%s/quesbatch?ids=%s", subject, URLEncoder.encode(sids, "UTF-8"));
//            url = String.format("http://api.jyeoo.com/v1/%s/quesbatch?ids=%s", subject, sids);
            url = String.format("http://api.jyeoo.com/v1/%s/counter/BatchQuesGet", subject, URLEncoder.encode(sids, "UTF-8"));
            System.out.println("url="+url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
        Map<String, String> headerMap = getHeaderMap(token);
        System.out.println("headerMap="+headerMap);

        try {
            return sendPost(url, JSONObject.parseObject("{id: '"+sids+"'}"),"Token " + token );
        } catch (ParseException e) {
            e.printStackTrace();
        } catch (IOException ioException) {
            ioException.printStackTrace();
        }

//        HttpResponse httpResponse = HttpKit.http().header(headerMap).param("id", sids).url(url).post();
//        System.out.println("res=" + JSON.toJSONString(httpResponse));
//        if (httpResponse.isSuccess()) {
//            return httpResponse.asString();
//        }


        throw new ServiceException("请求失败");
    }

    /**
     * 获取推荐试题
     * 
     * @param userId
     * @return
     * @throws ServiceException
     */
    public String getRelativeQueses(String userId, String subject, String id, int ps) throws ServiceException {
        String url = String.format("http://api.jyeoo.com/v1/%s/counter/QuesSame?id=%s&ps=%s", subject, id, ps);

        String key = url; // userId;// 去掉userId,让整个服务器用同一个请求,加快速度
        String resultCache = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isNotEmpty(resultCache)){
            return resultCache;
        }

        this.checkAndSaveReqParms(JyeooApiEnums.NOMINATE_QUES.getKey(), userId);

        String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
        Map<String, String> headerMap = getHeaderMap(token);
        HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
        if (httpResponse.isSuccess()) {
            String result = httpResponse.asString();
            cacheCommonTools.saveCacheValue(key, result, ToolsConst.DAY_SECOND);
//            System.out.println("------------->>>>>>>>>>>>>getRelativeQueses");
//            System.out.println(result);
            ToolsKit.Thread.execute(new Runnable() {
                @Override
                public void run() {
                    jyeooQuesAnalysisBuService.saveTitleByNominatequesString(result, subject, userId);
                }
            });

            return result;
        }
        throw new ServiceException("请求失败");
    }

    /**
     * 按章节或考点获取试题列表
     * 
     * @param userId
     * @param subject
     * @param tp
     * @param p1
     * @param p2
     * @param p3
     * @param cate
     * @param degree
     * @param sc
     * @param gc
     * @param rc
     * @param yc
     * @param ec
     * @param er
     * @param region
     * @param source
     * @param year
     * @param po
     * @param pd
     * @param pi
     * @param ps
     * @param onlyNos
     * @return
     * @throws ServiceException
     */
    public SearchObject searchQues(String userId, String subject, int tp, String p1, String p2, String p3, String cate, int degree, Boolean sc, Boolean gc,
                                   Boolean rc, Boolean yc, Boolean ec, Boolean er, String region, String source, String year, int po, int pd, int pi, int ps, int onlyNos)
            throws ServiceException {
        String url = null;
        try {
            url = String.format(
                    "http://api.jyeoo.com/v1/%s/counter/QuesQuery?tp=%s&p1=%s&p2=%s&p3=%s&ct=%s&dg=%s&sc=%s&gc=%s&rc=%s&yc=%s&ec=%s&er=%s&rg=%s&so=%s&yr=%s&po=%s&pd=%s&pi=%s&ps=%s&onlyNos=%s",
                    subject, tp, URLEncoder.encode(p1, "UTF-8"), URLEncoder.encode(p2, "UTF-8"), URLEncoder.encode(p3, "UTF-8"), cate, degree, sc, gc, rc, yc,
                    ec, er, region, source, year, po, pd, pi, ps, onlyNos);
            System.out.println("==================================================="+url);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        String key = url; // userId;// 去掉userId,让整个服务器用同一个请求,加快速度
        String resultCache = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isNotEmpty(resultCache)){
            return JsonKit.jsonParseObject(resultCache, SearchObject.class);
        }

        this.checkAndSaveReqParms(JyeooApiEnums.SEARCH_COND.getKey(), userId);

        String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
        Map<String, String> headerMap = getHeaderMap(token);
        HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();
        if (httpResponse.isSuccess()) {
            String result = httpResponse.asString();
            cacheCommonTools.saveCacheValue(key, result, ToolsConst.DAY_SECOND);
            ToolsKit.Thread.execute(new Runnable() {
                @Override
                public void run() {
                    jyeooQuesAnalysisBuService.saveTitleByCondString(result, subject, userId);
                }
            });
            return JsonKit.jsonParseObject(result, SearchObject.class);
        }
        throw new ServiceException("请求失败");
    }

    public String ocrQuesSearch(String userId, String imageUrl, String xk, String tx, String ps) {
        byte[] getData = new byte[0];
        try {
            getData = ToolsKit.Image.getByteImage(imageUrl);
            File tempFile = File.createTempFile("image",".png");
            ToolsKit.File.writeBytes(getData, tempFile);
            String filePath = tempFile.getAbsolutePath();
            long fileLength = tempFile.length();
            int i=10;
            if (fileLength/1024 >=200){
                File distFile = File.createTempFile("image",".png");
                while (fileLength/1024 >=200) {
                    i=i-3;
                    if (i<1){
                        break;
                    }
                    System.out.println((float) i/10);
                    ImageUtil.scale(tempFile,distFile, (float)i/10);
                    fileLength = distFile.length();
                }
                filePath = distFile.getAbsolutePath();
            }

            Map<String, String> fileMap = new HashMap<>();
//            System.out.println("<><><><><><><><><><><><><><><><><><><><><><><><>");
//            System.out.println(filePath);
            fileMap.put("file", filePath);

            xk = ToolsKit.isEmpty(xk)?"":xk;
            tx = ToolsKit.isEmpty(tx)?"":tx;
            ps = ToolsKit.isEmpty(ps)?"5":ps;
            String url = String.format("http://api.jyeoo.com/v1/counter/ocrquessearch?xk=%s&tx=%s&ps=%s", xk,tx,ps);
            String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
            Map<String, String> headerMap = getHeaderMap(token);
            String result = postFiles(url, headerMap, fileMap);
            ToolsKit.Thread.execute(new Runnable() {
                @Override
                public void run() {
                    jyeooQuesAnalysisBuService.saveTitleByOrcString(result, userId);
                }
            });

            return result;
        } catch (IOException ioException) {
            ioException.printStackTrace();
        }
        return null;
    }

    /**
     * 按章节或考点获取试题列表
     *
     * @param userId    当前用户id
     * @param subject   学科英文名称，不填则为全科
     * @param keyword   搜索词（不要超过100字）；
     * @param type      题型（默认为0）
     * @param pi        当前页（以1开始）
     * @param ps        每页记录数（小于等于10）
     * @return
     * @throws ServiceException
     */
    public SearchObject keySearch(String userId, String keyword,  String subject, int type, int pi, int ps)
            throws ServiceException {
        String url = null;
        try {
            url = String.format("http://api.jyeoo.com/v1/counter/QuesSearch?q=%s&pi=%s&ps=%s&t=%s&s=%s",URLEncoder.encode(keyword, "UTF-8"), pi, ps,type,subject);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        String key = url; // userId;// 去掉userId,让整个服务器用同一个请求,加快速度
        String resultCache = cacheCommonTools.getCacheValue(key, String.class);
        if (ToolsKit.isNotEmpty(resultCache)){
            return JsonKit.jsonParseObject(resultCache, SearchObject.class);
        }

        // 检查下请求次数
        this.checkAndSaveReqParms(JyeooApiEnums.SEARCH_KEYWORD.getKey(), userId);

        String token = getToken(userId, Constant.JYEOO_DEFAULT_PWD);
        Map<String, String> headerMap = getHeaderMap(token);
        HttpResponse httpResponse = HttpKit.http().header(headerMap).url(url).get();

        if (httpResponse.isSuccess()) {
            String result = httpResponse.asString();
//            System.out.println("------------->>>>>>>>>>>>>keySearch");
//            System.out.println(result);
            cacheCommonTools.saveCacheValue(key, result, ToolsConst.DAY_SECOND);
            ToolsKit.Thread.execute(new Runnable() {
                @Override
                public void run() {
                    jyeooQuesAnalysisBuService.saveTitleByKeywordString(result, subject, userId);
                }
            });
            return JsonKit.jsonParseObject(result, SearchObject.class);
        }
        throw new ServiceException("请求失败");
    }

    private void checkAndSaveReqParms(String key, String userId){
        JyeooApiEnums jy = JyeooApiEnums.getMap().get(key);
        //判断当前接口是否请求上限了,没达到上限的话,做保存/缓存
        String reqNumKey = jy.getCacheKey() + userId + ":" + ToolsKit.Date.format(new Date(), "yyyyMMdd");
        Integer reqNum = cacheCommonTools.getCacheValue(reqNumKey, Integer.class);
        if (reqNum == null){
            reqNum = 0;
        }
        if (reqNum>=jy.getCanReqCount()){
            throw new ServiceException(jy.getExceptionDesc());
        }
        reqNum++;
        cacheCommonTools.saveCacheValue(reqNumKey, reqNum, ToolsConst.DAY_SECOND);
        jyeooUserReqCountBuService.saveJyeooUserReqCount(userId, ToolsKit.Date.format(new Date(), "yyyyMMdd"), key, reqNum);
    }

    /**
     * 获取请求头信息
     * 
     * @param token
     * @return
     */
    private Map<String, String> getHeaderMap(String token) {
        Map<String, String> headerMap = new HashMap<String, String>();
        headerMap.put("accept", "application/json; charset=UTF-8");
        headerMap.put("Content-Type", "application/json; charset=UTF-8");
        headerMap.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36");
        if (ToolsKit.isNotEmpty(token)) {
            headerMap.put("authorization", "Token " + token);
        }
        return headerMap;
    }

    public String postFiles(String urlStr, Map<String, String> textMap,
                        Map<String, String> fileMap) throws IOException {
        String res = "";
        HttpURLConnection conn = null;
        String BOUNDARY = "---------------------------123821742118716"; //boundary就是request头和上传文件内容的分隔符
        try {
            URL url = new URL(urlStr);
            conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(30000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
            conn.setRequestProperty("authorization", textMap.get("authorization"));
            //conn.setRequestProperty("authorization", "Token \"13FDC5CECF6423CB04C79BB2C50CF69A59D2019A0211C255438A2B55404073F63FFE1FB19586CFD103FD0F4EC2A5414A5044BB275D2AFA03A355F5CF67668204C91F78D05DEAF634E24FEB54469F929E0ACD64BE367D0297B93C7FA75277C7A7B3CB138622A38958705A1BF340EA244D4B8AB35A4B43311DE94B44CF5021A7C8F3E7378F2FF5171B2A85A00498AE054DB3414B5892F3D0E8CC562297C0D76E3676958B49EB59EBA3\"");
            conn.setRequestProperty("accept","application/json; charset=UTF-8");
//            headerMap.put("authorization","Token \"13FDC5CECF6423CB04C79BB2C50CF69A59D2019A0211C255438A2B55404073F63FFE1FB19586CFD103FD0F4EC2A5414A5044BB275D2AFA03A355F5CF67668204C91F78D05DEAF634E24FEB54469F929E0ACD64BE367D0297B93C7FA75277C7A7B3CB138622A38958705A1BF340EA244D4B8AB35A4B43311DE94B44CF5021A7C8F3E7378F2FF5171B2A85A00498AE054DB3414B5892F3D0E8CC562297C0D76E3676958B49EB59EBA3\"");
//            headerMap.put("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36");
//            headerMap.put("accept","application/json; charset=UTF-8");
//            headerMap.put("Content-Type","application/json; charset=UTF-8");


            OutputStream out = new DataOutputStream(conn.getOutputStream());
            // text
            if (textMap != null) {
                StringBuffer strBuf = new StringBuffer();
                Iterator iter = textMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    strBuf.append("\r\n").append("--").append(BOUNDARY).append(
                            "\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\""
                            + inputName + "\"\r\n\r\n");
                    strBuf.append(inputValue);
                }
                out.write(strBuf.toString().getBytes());
            }

            // file
            if (fileMap != null) {
                Iterator iter = fileMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    File file = new File(inputValue);
                    String filename = file.getName();
                    String contentType = new MimetypesFileTypeMap()
                            .getContentType(file);
                    if (filename.endsWith(".png")) {
                        contentType = "image/png";
                    } else if (filename.endsWith(".jpg")){
                        contentType = "image/jpeg";
                    }
                    if (contentType == null || contentType.equals("")) {
                        contentType = "application/octet-stream";
                    }

                    StringBuffer strBuf = new StringBuffer();
                    strBuf.append("\r\n").append("--").append(BOUNDARY).append(
                            "\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\""
                            + inputName + "\"; filename=\"" + filename
                            + "\"\r\n");
                    strBuf.append("Content-Type:" + contentType + "\r\n\r\n");

                    out.write(strBuf.toString().getBytes());

                    DataInputStream in = new DataInputStream(
                            new FileInputStream(file));
                    int bytes = 0;
                    byte[] bufferOut = new byte[1024];
                    while ((bytes = in.read(bufferOut)) != -1) {
                        out.write(bufferOut, 0, bytes);
                    }
                    in.close();
                }
            }

            byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
            out.write(endData);
            out.flush();
            out.close();

            // 读取返回数据
            StringBuffer strBuf = new StringBuffer();
            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    conn.getInputStream()));
            String line = null;
            while ((line = reader.readLine()) != null) {
                strBuf.append(line).append("\n");
            }
            res = strBuf.toString();
            reader.close();
            reader = null;
        } catch (Exception e) {
            System.out.println("发送POST请求出错。" + urlStr);
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.disconnect();
                conn = null;
            }
        }
        return res;
    }

    public String sendPost(String url, JSONObject jsonObject, String authorization) throws ParseException, IOException{
        String body = "";

        //创建httpclient对象
        CloseableHttpClient client = HttpClients.createDefault();
        //创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);

        //装填参数
        StringEntity s = new StringEntity(jsonObject.toString(), "utf-8");
        s.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE,
                "application/json"));
        //设置参数到请求对象中
        httpPost.setEntity(s);

        //设置header信息
        //指定报文头【Content-type】、【User-Agent】
//        httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        httpPost.setHeader("authorization", authorization);

        //执行请求操作，并拿到结果（同步阻塞）
        CloseableHttpResponse response = client.execute(httpPost);
        //获取结果实体
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            //按指定编码转换结果实体为String类型
            body = EntityUtils.toString(entity, "utf-8");
        }
        EntityUtils.consume(entity);
        //释放链接
        response.close();
        return body;
    }
}