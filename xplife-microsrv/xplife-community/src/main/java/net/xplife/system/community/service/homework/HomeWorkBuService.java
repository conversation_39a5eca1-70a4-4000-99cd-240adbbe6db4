package net.xplife.system.community.service.homework;

import com.alibaba.fastjson.JSONObject;
import net.xplife.system.account.interfaces.user.UserAccountService;
import net.xplife.system.community.dao.homework.HomeWorkDao;
import net.xplife.system.community.dto.homework.HomeWorkDto;
import net.xplife.system.community.entity.homework.HomeWork;
import net.xplife.system.community.enums.homework.HomeWorkSubjectEnums;
import net.xplife.system.community.enums.printer.PrintFileTypeEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.config.CommonProperties;
import net.xplife.system.web.core.I18nUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/9 9:09
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class HomeWorkBuService {

    @Autowired
    private HomeWorkDao homeWorkDao;

    @Autowired
    private I18nUtils i18nUtils;

    @Autowired
    private CommonProperties commonProperties;

    @Autowired
    private UserAccountService userAccountService;

    public List<HomeWorkDto> getHomeWorkList(int pageNo, int pageSize, String printerSn, String subject, Locale locale) {
        List<HomeWork> list = homeWorkDao.findHomeWorkList(pageNo, pageSize,printerSn,  subject);
        List<HomeWorkDto> result = new ArrayList<>();
        if (ToolsKit.isNotEmpty(list)){
            for (HomeWork homework: list) {
                HomeWorkDto dto = new HomeWorkDto();
                ToolsKit.Bean.copyProperties(homework, dto);
                if (ToolsKit.isNotEmpty(dto.getSubject()) && HomeWorkSubjectEnums.getMap().get(dto.getSubject())!=null){

                    if (ToolsKit.isNotEmpty(homework.getFileType())){
                        dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain() , PrintFileTypeEnums.getMap().get(dto.getFileType().toLowerCase())));
                    } else {
                        dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain() , PrintFileTypeEnums.TXT.getIcon()));
                    }

//                    dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain() , HomeWorkSubjectEnums.getMap().get(dto.getSubject()).getIcon()));

                    String name = i18nUtils.getKey("subject_name_"+HomeWorkSubjectEnums.getMap().get(dto.getSubject()), locale);
                    if (ToolsKit.isNotEmpty(name)){
                        dto.setSubjectName(name);
                    } else {
                        dto.setSubjectName(HomeWorkSubjectEnums.getMap().get(dto.getSubject()).getValue());
                    }
                }
                dto.setCreatetime(ToolsKit.Date.format(homework.getCreatetime(), "yyyy-MM-dd HH:mm"));
                result.add(dto);
            }
        }
        return result;
    }

    public HomeWork save(HomeWork homeWork, String userId) {
        homeWork.setUserName(userAccountService.getUserInfo(userId).getUserInfoDto().getNickName());
        homeWork.setId(null);
        ToolsKit.setIdEntityData(homeWork, userId);

        System.out.println(JSONObject.toJSON(homeWork));

        homeWorkDao.saveEntity(homeWork);
        return homeWork;
    }

    public boolean deleteById(String ids) {
        if (ToolsKit.isEmpty(ids)) {
            throw new ServiceException("缺省必要参数");
        }
        String [] idsArray = ids.split(",");
        for (String id: idsArray) {
            if (ToolsKit.isNotEmpty(id)){
                homeWorkDao.delById(id);
            }
        }
        return true;
    }

    public List<Map<String, String>> getSubjectList(Locale locale) {
        List<Map<String, String>> result = new ArrayList<>();
        for (HomeWorkSubjectEnums homeWorkSubjectEnums: HomeWorkSubjectEnums.values()) {
            Map<String, String> map = new HashMap<>();
            map.put("code", homeWorkSubjectEnums.getCode());

            String name = i18nUtils.getKey("subject_name_"+homeWorkSubjectEnums.getCode(), locale);

            if (ToolsKit.isNotEmpty(name)){
                map.put("name", name);
            } else {
                map.put("name", homeWorkSubjectEnums.getValue());
            }

            map.put("icon", ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain() , homeWorkSubjectEnums.getIcon()) );

            result.add(map);
        }
        return result;
    }

    public List<Map<String, String>> getSubjectTabs(String userId, String printerSn, Locale locale) {
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, String> allTab = new HashMap<>();
        allTab.put("code", "");
        String allTabName = i18nUtils.getKey("subject_name_All", locale);
        allTab.put("name", allTabName);
        result.add(allTab);

        List<HomeWork> list = homeWorkDao.findHomeWorkList(1, 999, printerSn,  null);
        if (ToolsKit.isNotEmpty(list)){
            for (HomeWorkSubjectEnums homeWorkSubjectEnums: HomeWorkSubjectEnums.values()) {

                boolean isExist = false;
                for (HomeWork homeWork:list) {
                    if(homeWorkSubjectEnums.getCode().equals(homeWork.getSubject())){
                        isExist = true;
                    }
                }

                if (!isExist){
                    continue;
                }

                Map<String, String> map = new HashMap<>();
                map.put("code", homeWorkSubjectEnums.getCode());

                String name = i18nUtils.getKey("subject_name_"+homeWorkSubjectEnums.getCode(), locale);

                if (ToolsKit.isNotEmpty(name)){
                    map.put("name", name);
                } else {
                    map.put("name", homeWorkSubjectEnums.getValue());
                }

//                map.put("icon", ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain() , homeWorkSubjectEnums.getIcon()) );

                result.add(map);
            }
        }

        return result;
    }
}
