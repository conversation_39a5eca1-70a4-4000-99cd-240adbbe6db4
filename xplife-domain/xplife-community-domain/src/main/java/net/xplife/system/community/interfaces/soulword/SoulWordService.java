package net.xplife.system.community.interfaces.soulword;

import net.xplife.system.community.entity.soulword.SoulWord;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface SoulWordService {
    /***
     * 获取分页
     * @param pageno
     * @param pagesize
     * @param content 查询内容
     * @return
     */
    @RequestMapping(value = "/community/v1/soulword/getPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<SoulWord> findPage(@RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize, @RequestParam("content") String content);

    /***
     * 删除
     * @param id
     */
    @RequestMapping(value = "/community/v1/soulword/delete", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void delete(@RequestParam("id") String id);

    /**
     * 新增或修改
     * @param soulWord
     */
    @RequestMapping(value = "/community/v1/soulword/saveorupdate", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public SoulWord saveOrUpdate(@RequestBody SoulWord soulWord);
}
