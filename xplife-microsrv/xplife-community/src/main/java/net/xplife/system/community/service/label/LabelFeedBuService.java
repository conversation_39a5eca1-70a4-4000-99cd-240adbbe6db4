package net.xplife.system.community.service.label;

import java.util.*;

import net.xplife.system.mongo.common.Page;
import net.xplife.system.community.cache.feed.FeedNoteCacheService;
import net.xplife.system.community.enums.feed.FeedNoteStickEnums;
import net.xplife.system.community.service.feed.GiveLikeFeedBuService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.WebKit;
import net.xplife.system.web.config.CommonProperties;
import net.xplife.system.web.dto.PicInfoDto;
import net.xplife.system.community.cache.label.LabelFeedCacheService;
import net.xplife.system.community.dao.label.LabelFeedDao;
import net.xplife.system.community.dto.feed.FeedInfoDto;
import net.xplife.system.community.dto.feed.PicDto;
import net.xplife.system.community.entity.feed.FeedNote;
import net.xplife.system.community.entity.feed.FeedStatistics;
import net.xplife.system.community.entity.label.LabelFeed;
import net.xplife.system.community.enums.label.LabelTypeEnums;
import net.xplife.system.community.service.feed.FeedNoteBuService;
import net.xplife.system.community.service.feed.FeedStatisticsBuService;
import net.xplife.system.community.service.feed.ShieldUserBuService;
import net.xplife.system.community.utils.Constant;
import net.xplife.system.community.vo.feed.PicVo;
import cn.hutool.crypto.SecureUtil;

@Service
public class LabelFeedBuService {
    @Autowired
    private LabelFeedDao labelFeedDao;
    @Autowired
    private LabelFeedCacheService labelFeedCacheService;
    @Autowired
    private FeedStatisticsBuService feedStatisticsBuService;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private FeedNoteBuService feedNoteBuService;
    @Autowired
    private ShieldUserBuService shieldUserBuService;
    @Autowired
    private GiveLikeFeedBuService giveLikeFeedBuService;
    @Autowired
    private FeedNoteCacheService feedNoteCacheService;
    @Autowired
    private LabelBuService labelBuService;

    public void save(LabelFeed labelFeed) {
        labelFeedDao.saveEntity(labelFeed);
        labelFeedCacheService.saveLabelFeed(labelFeed);
    }

    /**
     * 初始化标签动态列表
     *
     * @param type 类型
     * @return
     */
    public List<LabelFeed> initLabelFeedList(String labelId, int type) {
        List<LabelFeed> labelFeedList = new ArrayList<>();
        List<FeedNote> list = feedNoteBuService.findListByLabelId(labelId);
        for (FeedNote feedNote : list) {
            List<LabelFeed> temp = labelFeedDao.findLabelFeedByFeedId(feedNote.getId());
            if (ToolsKit.isNotEmpty(temp)) {
                for (LabelFeed labelFeed : temp) {
                    labelFeedCacheService.addLabelFeedIdToList(labelId, labelFeed.getId(), labelFeed.getCreatetime().getTime());
                }
                labelFeedList.addAll(temp);
            }
        }
//        List<LabelFeed> labelFeedList = labelFeedDao.findLabelFeedList(labelId, 0, 1000, type, null);
//        if (ToolsKit.isNotEmpty(labelFeedList)) {
//            ToolsKit.Thread.execute(new Runnable() {
//                @Override
//                public void run() {
//                    for (LabelFeed labelFeed : labelFeedList) {
//                        labelFeedCacheService.addLabelFeedIdToList(labelId, labelFeed.getId(), labelFeed.getCreatetime().getTime());
//                    }
//                }
//            });
//        }
        return labelFeedList;
    }

    /**
     * 获取标签动态ID列表
     *
     * @param labelId  标签ID
     * @param page     当前页
     * @param pageSize 每页大小
     * @param lastId   最后一条数据ID
     * @return 标签动态ID列表
     */
    private List<String> getLabelFeedIdsList(String labelId, int page, int pageSize, int type, String lastId) {
        // 分页太恶心了，此处不得已的修改，最主要分页的对象时feedId，每个自己feedNote又有1--9个label，此处用page来分label非常不妥
        List<String> feedIdlist = new ArrayList<>();
        int begin = page * pageSize + 1;
        int end = (page + 1) * pageSize + 1;
        List<String> idsList = new ArrayList<>();
        if (!labelFeedCacheService.existsList(labelId)) {
            List<LabelFeed> labelFeedList = this.initLabelFeedList(labelId, type);
            if (ToolsKit.isNotEmpty(labelFeedList)) {
                for (LabelFeed labelfeed : labelFeedList) {
                    FeedNote feedNote = feedNoteBuService.getFeedNoteById(labelfeed.getFeedId());
                    if (ToolsKit.isEmpty(feedNote)) {
                        continue;
                    }

                    if (!feedIdlist.contains(labelfeed.getFeedId())) {
                        feedIdlist.add(labelfeed.getFeedId());
                    }
                    if (feedIdlist.size() >= begin && feedIdlist.size() < end) {
                        idsList.add(labelfeed.getId());
                    }
                }
            }
        } else {
            List<String> ids = labelFeedCacheService.getLabelFeedIdsList(labelId);
            if (ToolsKit.isNotEmpty(ids)) {
                // 查询所有结果的id，然后一个个来筛选，此处非常非常不妥，但暂时想不到用什么处理，此处500条记录
                for (String id : ids) {
                    LabelFeed labelFeed = getLabelFeedById(id);
                    //LabelFeed labelFeed = labelFeedCacheService.getLabelFeedById(id);
                    if (ToolsKit.isEmpty(labelFeed)) {
                        continue;
                    }
                    FeedNote feedNote = feedNoteBuService.getFeedNoteById(labelFeed.getFeedId());
                    if (ToolsKit.isEmpty(feedNote)) {
                        continue;
                    }
                    if (!feedIdlist.contains(labelFeed.getFeedId())) {
                        feedIdlist.add(labelFeed.getFeedId());
                    }
                    if (feedIdlist.size() >= begin && feedIdlist.size() < end) {
                        idsList.add(labelFeed.getId());
                    }
                }
            }
        }
        return idsList;
    }

    /**
     * 获取标签动态ID列表
     *
     * @param labelId  标签ID
     * @param page     当前页
     * @param pageSize 每页大小
     * @param lastId   最后一条数据ID
     * @return 标签动态ID列表
     */
    private List<String> getLabelFeedIdsList(String keyword, String gradeLevel, String subject, String labelId, int page, int pageSize, int type, String lastId) {
        // 分页太恶心了，此处不得已的修改，最主要分页的对象时feedId，每个自己feedNote又有1--9个label，此处用page来分label非常不妥
        List<String> feedIdlist = new ArrayList<>();
        int begin = page * pageSize + 1;
        int end = (page + 1) * pageSize + 1;
        List<String> idsList = new ArrayList<>();
        if (!labelFeedCacheService.existsList(labelId)) {
            List<LabelFeed> labelFeedList = this.initLabelFeedList(labelId, type);
            if (ToolsKit.isNotEmpty(labelFeedList)) {
                for (LabelFeed labelfeed : labelFeedList) {
                    FeedNote feedNote = feedNoteBuService.getFeedNoteById(labelfeed.getFeedId());
                    if (ToolsKit.isEmpty(feedNote)) {
                        continue;
                    }
                    if (ToolsKit.isNotEmpty(keyword)) {
                        if (ToolsKit.isNotEmpty(feedNote.getKeyword()) &&
                                !feedNote.getKeyword().contains(keyword)) {
                            continue;
                        }
                    }
                    if (ToolsKit.isNotEmpty(gradeLevel) && !gradeLevel.equals(feedNote.getGradeLevel())) {
                        continue;
                    }
                    if (ToolsKit.isNotEmpty(subject) && !subject.equals(feedNote.getSubject())) {
                        continue;
                    }

                    if (!feedIdlist.contains(labelfeed.getFeedId())) {
                        feedIdlist.add(labelfeed.getFeedId());
                    }
                    if (feedIdlist.size() >= begin && feedIdlist.size() < end) {
                        idsList.add(labelfeed.getId());
                    }
                }
            }
        } else {
            List<String> ids = labelFeedCacheService.getLabelFeedIdsList(labelId);
            if (ToolsKit.isNotEmpty(ids)) {
                // 查询所有结果的id，然后一个个来筛选，此处非常非常不妥，但暂时想不到用什么处理，此处500条记录
                for (String id : ids) {
                    LabelFeed labelFeed = labelFeedCacheService.getLabelFeedById(id);
                    if (ToolsKit.isEmpty(labelFeed)) {
                        continue;
                    }
                    FeedNote feedNote = feedNoteBuService.getFeedNoteById(labelFeed.getFeedId());
                    if (ToolsKit.isEmpty(feedNote)) {
                        continue;
                    }
                    if (ToolsKit.isNotEmpty(keyword)) {
                        if (ToolsKit.isEmpty(feedNote.getKeyword())) {
                            continue;
                        }
                        if (!feedNote.getKeyword().contains(keyword)) {
                            continue;
                        }
                    }
                    if (ToolsKit.isNotEmpty(gradeLevel) && !gradeLevel.equals(feedNote.getGradeLevel())) {
                        continue;
                    }
                    if (ToolsKit.isNotEmpty(subject) && !subject.equals(feedNote.getSubject())) {
                        continue;
                    }
                    if (!feedIdlist.contains(labelFeed.getFeedId())) {
                        feedIdlist.add(labelFeed.getFeedId());
                    }
                    if (feedIdlist.size() >= begin && feedIdlist.size() < end) {
                        idsList.add(labelFeed.getId());
                    }
                }
            }
        }
        return idsList;
    }

    /**
     * 获取标签动态列表
     *
     * @param userId   用户ID
     * @param labelId  标签ID
     * @param pageNo
     * @param pageSize
     * @param lastId
     * @return
     * @throws ServiceException
     */
    public List<FeedInfoDto> getLabelFeedList(String userId, String labelId, int pageNo, int pageSize, String lastId, String version) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        List<FeedInfoDto> dtoList = new ArrayList<FeedInfoDto>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            if (ToolsKit.isEmpty(labelId)) {
                labelId = StringUtils.EMPTY;
            }

            int type = ToolsKit.isEmpty(labelId) ? LabelTypeEnums.ALL.getValue() : LabelTypeEnums.LABEL.getValue();
            List<String> checkPics = new ArrayList<>();
            pageSize = 5;
            int MAX_COUNT = 20;
            int count = 0;
            Map<String, FeedInfoDto> dtoMaps = new HashMap<>();
            List<String> idsList = this.getLabelFeedIdsList(labelId, pageNo, pageSize, type, lastId);
//                while (ToolsKit.isNotEmpty(idsList) && idsList.size() > 0) {
//                    count++;
//                    if (count > MAX_COUNT) {
//                        break;
//                    }
//                    String innerLastId = null;
//                    for (String id : idsList) {
//                        LabelFeed labelFeed = this.getLabelFeedById(id);
//                        if (ToolsKit.isNotEmpty(labelFeed)) {
//                            FeedNote feedNote = feedNoteBuService.getFeedNoteById(labelFeed.getFeedId());
//                            if (ToolsKit.isNotEmpty(feedNote)) {
//                                String tmpPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), labelFeed.getPic().getPic());
//                                String flag = SecureUtil.md5(tmpPic);
//                                if (checkPics.contains(flag)) {
//                                    // 图片已添加过
//                                    continue;
//                                }
//                                checkPics.add(flag);
//
//                                if (!dtoMaps.containsKey(labelFeed.getFeedId())) {
//                                    if (dtoList.size() >= pageSize) {
//                                        break;
//                                    }
//
//                                    //maps.put(labelFeed.getFeedId(), new ArrayList<>());
//                                    FeedInfoDto dtoTemp = new FeedInfoDto();
//                                    dtoMaps.put(labelFeed.getFeedId(), dtoTemp);
//                                    dtoTemp.setPicDto(new ArrayList<PicDto>());
//                                    dtoTemp.setSmallPicDto(new ArrayList<PicDto>());
//                                    dtoList.add(dtoTemp);
//                                }
//
//                                //maps.get(labelFeed.getFeedId()).add(id);
//                                FeedInfoDto dto = dtoMaps.get(labelFeed.getFeedId());
//
//                                dto.setLastId(id);
//                                innerLastId = id;
//                                feedNoteBuService.fillUserInfo(feedNote.getCreateuserid(), dto);
//
//                                // 大图片统计信息
//                                PicDto picDto = feedNoteBuService.getPicDto(feedNote.getId(), userId, labelFeed.getPic());
//                                FeedStatistics feedStatistics = feedStatisticsBuService.getFeedStatistics(feedNote.getId());
//                                picDto.setLikeNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getLikeVo(), flag));
//                                picDto.setDownLoadNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getDownLoadVo(), flag));
//                                picDto.setPrintNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getPrintVo(), flag));
//                                dto.setLikeNum(dto.getLikeNum() + picDto.getLikeNum());
//                                dto.setDownLoadNum(dto.getDownLoadNum() + picDto.getDownLoadNum());
//                                dto.setPrintNum(dto.getPrintNum() + picDto.getPrintNum());
//                                dto.getPicDto().add(picDto);
//                                dto.setShareNum(feedStatistics.getShareNum());
//                                dto.setGiveLikeNum(feedStatistics.getGiveLikeNum());
//
//                                // 小图
//                                PicDto smallPicDto = new PicDto();
//                                ToolsKit.Bean.copyProperties(picDto, smallPicDto);
//                                if (picDto.getHeight() >= Constant.OSS_MAX_VALUE || picDto.getWidth() >= Constant.OSS_MAX_VALUE) {
//                                    smallPicDto.setPic(picDto.getPic());
//                                } else {
//                                    smallPicDto.setPic(picDto.getPic() + Constant.FEED_SUFFIX);
//                                }
//                                dto.getSmallPicDto().add(smallPicDto);
//
//                                dto.setLabelMap(feedNoteBuService.getLabelMap(feedNote.getLabelIds()));
//                                feedNoteBuService.fillStatisticsNum(feedStatistics, labelFeed.getPic(), dto);
//                                dto.setContent(feedNote.getContent());
//                                dto.setTitle(feedNote.getTitle());
//                                dto.setFeedId(feedNote.getId());
//                                dto.setCreateTime(feedNote.getCreatetime());
//                                if (ToolsKit.isNotEmpty(feedNote.getHtmlUrl())) {
//                                    dto.setHtmlUrl(feedNote.getHtmlUrl());
//                                } else {
//                                    if (ToolUtils.compareVersion(Constant.VERSION_310, version)){
//                                        dto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.Label_INFO_URL + "?feedid="
//                                                + feedNote.getId());
//                                    } else {
//                                        dto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.OLD_FEED_INFO_URL + "?feedid="
//                                                + feedNote.getId());
//                                    }
//                                }
//                                dto.setShareUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.SHAREVIEW_FEED_INFO_URL + "?feedid=" + dto.getFeedId());
//                                dto.setIsFriend(FriendsStatusTypeEnums.NO_FRIEND.getValue());
////                                System.out.println("----------------------------------------------------------" + giveLikeFeedBuService.hasGiveLikeFeed(feedNote.getId(), userId));
//                                //增加是否已点赞
//                                if (giveLikeFeedBuService.hasGiveLikeFeed(feedNote.getId(), userId)){
//                                    dto.setHadGivedLike(1);
//                                } else {
//                                    dto.setHadGivedLike(0);
//                                }
//                                //添加关注状态
//                                dto.setIsFollow(friendsFollowService.getFollowStatus(userId, feedNote.getCreateuserid()));
//                            }
//                        }
//                    }
//
//                    pageNo = pageNo + 1;
//                    idsList = this.getLabelFeedIdsList(labelId, pageNo, pageSize, type, innerLastId);
//                }
            for (String id : idsList) {
                LabelFeed labelFeed = this.getLabelFeedById(id);
                if (ToolsKit.isNotEmpty(labelFeed)) {
                    FeedNote feedNote = feedNoteBuService.getFeedNoteById(labelFeed.getFeedId());
                    if (ToolsKit.isNotEmpty(feedNote)) {
                        String tmpPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), labelFeed.getPic().getPic());
                        String flag = SecureUtil.md5(tmpPic);
                        if (checkPics.contains(flag)) {
                            // 图片已添加过
                            continue;
                        }
                        checkPics.add(flag);

                        if (!dtoMaps.containsKey(labelFeed.getFeedId())) {
                            if (dtoList.size() >= pageSize) {
                                break;
                            }

                            //maps.put(labelFeed.getFeedId(), new ArrayList<>());
                            FeedInfoDto dtoTemp = new FeedInfoDto();
                            dtoMaps.put(labelFeed.getFeedId(), dtoTemp);
                            dtoTemp.setPicDto(new ArrayList<PicDto>());
                            dtoTemp.setSmallPicDto(new ArrayList<PicDto>());
                            dtoList.add(dtoTemp);
                        }

                        //maps.get(labelFeed.getFeedId()).add(id);
                        FeedInfoDto dto = dtoMaps.get(labelFeed.getFeedId());

                        dto.setLastId(id);
//                            innerLastId = id;
                        feedNoteBuService.fillUserInfo(feedNote.getCreateuserid(), dto);

                        // 大图片统计信息
                        PicDto picDto = feedNoteBuService.getPicDto(feedNote.getId(), userId, labelFeed.getPic());
                        FeedStatistics feedStatistics = feedStatisticsBuService.getFeedStatistics(feedNote.getId());
                        picDto.setLikeNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getLikeVo(), flag));
                        picDto.setDownLoadNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getDownLoadVo(), flag));
                        picDto.setPrintNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getPrintVo(), flag));
                        dto.setLikeNum(dto.getLikeNum() + picDto.getLikeNum());
                        dto.setDownLoadNum(dto.getDownLoadNum() + picDto.getDownLoadNum());
                        dto.setPrintNum(dto.getPrintNum() + picDto.getPrintNum());
                        dto.getPicDto().add(picDto);
                        dto.setShareNum(feedStatistics.getShareNum());
                        dto.setGiveLikeNum(feedStatistics.getGiveLikeNum());

                        // 小图
                        PicDto smallPicDto = new PicDto();
                        ToolsKit.Bean.copyProperties(picDto, smallPicDto);
                        if (picDto.getHeight() >= Constant.OSS_MAX_VALUE || picDto.getWidth() >= Constant.OSS_MAX_VALUE) {
                            smallPicDto.setPic(picDto.getPic());
                        } else {
                            smallPicDto.setPic(picDto.getPic() + Constant.FEED_SUFFIX);
                        }
                        dto.getSmallPicDto().add(smallPicDto);

                        dto.setLabelMap(feedNoteBuService.getLabelMap(feedNote.getLabelIds()));
                        feedNoteBuService.fillStatisticsNum(feedStatistics, labelFeed.getPic(), dto);
                        dto.setContent(feedNote.getContent());
                        dto.setTitle(feedNote.getTitle());
                        dto.setFeedId(feedNote.getId());
                        dto.setCreateTime(feedNote.getCreatetime());
                        if (ToolsKit.isNotEmpty(feedNote.getHtmlUrl())) {
                            dto.setHtmlUrl(feedNote.getHtmlUrl());
                        } else {
                            dto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.Label_INFO_URL + "?feedid="
                                    + feedNote.getId());
                        }
                        dto.setShareUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.SHAREVIEW_FEED_INFO_URL + "?feedid=" + dto.getFeedId());
//                        dto.setIsFriend(FriendsStatusTypeEnums.NO_FRIEND.getValue());
//                                System.out.println("----------------------------------------------------------" + giveLikeFeedBuService.hasGiveLikeFeed(feedNote.getId(), userId));
                        dto.setIsFriend(0);
                        //增加是否已点赞
                        if (giveLikeFeedBuService.hasGiveLikeFeed(feedNote.getId(), userId)) {
                            dto.setHadGivedLike(1);
                        } else {
                            dto.setHadGivedLike(0);
                        }
                        dto.setIsFollow(0);
                        //添加关注状态
//                        dto.setIsFollow(friendsFollowService.getFollowStatus(userId, feedNote.getCreateuserid()));
                    }
                }
            }

            // V2.10.0 倒排序，与社区一致
            for (FeedInfoDto dtoItem : dtoList) {
                List<PicDto> picList = new ArrayList<>();
                if (dtoItem.getPicDto() != null) {
                    for (int i = dtoItem.getPicDto().size() - 1; i >= 0; i--) {
                        picList.add(dtoItem.getPicDto().get(i));
                    }
                    dtoItem.getPicDto().clear();
                    dtoItem.getPicDto().addAll(picList);
                }

                List<PicDto> smallPicList = new ArrayList<>();
                if (dtoItem.getSmallPicDto() != null) {
                    for (int i = dtoItem.getSmallPicDto().size() - 1; i >= 0; i--) {
                        smallPicList.add(dtoItem.getSmallPicDto().get(i));
                    }
                    dtoItem.getSmallPicDto().clear();
                    dtoItem.getSmallPicDto().addAll(smallPicList);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }

    /**
     * 获取标签动态列表
     *
     * @param userId   用户ID
     * @param labelId  标签ID
     * @param pageNo
     * @param pageSize
     * @param lastId
     * @return
     * @throws ServiceException
     */
    public List<FeedInfoDto> getLabelFeedListV400(String userId, String labelId, int pageNo, int pageSize, String lastId, String version) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }

        // 获取学习的labelid
        String studyLabelId = labelBuService.getLabelListStudy();

        List<FeedInfoDto> dtoList = new ArrayList<FeedInfoDto>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            if (ToolsKit.isEmpty(labelId)) {
                labelId = StringUtils.EMPTY;
            }

            int type = ToolsKit.isEmpty(labelId) ? LabelTypeEnums.ALL.getValue() : LabelTypeEnums.LABEL.getValue();
            // 从 2.4.0 版本开始，素材部分去掉屏蔽用户功能，并且素材按 FeedId 分组返回，默认每页 5 组 Update By RabyGao on 2020-01-03
            List<String> checkPics = new ArrayList<>();
            // pageSize = 20;
            int MAX_COUNT = 20;
            int count = 0;
            Map<String, FeedInfoDto> dtoMaps = new HashMap<>();
            List<String> idsList = this.getLabelFeedIdsList(labelId, pageNo, pageSize, type, lastId);
//            while (ToolsKit.isNotEmpty(idsList) && idsList.size() > 0) {
//                count++;
//                if (count > MAX_COUNT) {
//                    break;
//                }
//                String innerLastId = null;
//                for (String id : idsList) {
//                    LabelFeed labelFeed = this.getLabelFeedById(id);
//                    if (ToolsKit.isNotEmpty(labelFeed)) {
//                        FeedNote feedNote = feedNoteBuService.getFeedNoteById(labelFeed.getFeedId());
//                        if (ToolsKit.isNotEmpty(feedNote)) {
//                            // 置顶记录过滤掉先
//                            if (feedNote.getStickNum()== FeedNoteStickEnums.TOP.getKey()) {
//                                continue;
//                            }
//                            String tmpPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), labelFeed.getPic().getPic());
//                            String flag = SecureUtil.md5(tmpPic);
//                            if (checkPics.contains(flag)) {
//                                // 图片已添加过
//                                continue;
//                            }
//                            checkPics.add(flag);
//
//                            if (!dtoMaps.containsKey(labelFeed.getFeedId())) {
//                                if (dtoList.size() >= pageSize) {
//                                    break;
//                                }
//
//                                //maps.put(labelFeed.getFeedId(), new ArrayList<>());
//                                FeedInfoDto dtoTemp = new FeedInfoDto();
//                                dtoMaps.put(labelFeed.getFeedId(), dtoTemp);
//                                dtoTemp.setPicDto(new ArrayList<PicDto>());
//                                dtoTemp.setSmallPicDto(new ArrayList<PicDto>());
//
//                                // 设置精华标志
//                                if (feedNote.getStickNum()!= FeedNoteStickEnums.NONE.getKey()){
//                                    Map<String, Object> map = new HashMap<>();
//                                    map.put("key", feedNote.getStickNum());
//                                    map.put("name", FeedNoteStickEnums.getMap().get(feedNote.getStickNum()).getDesc());
//                                    map.put("icon", ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), FeedNoteStickEnums.getMap().get(feedNote.getStickNum()).getIcon()));
//                                    dtoTemp.setStickObj(map);
//                                } else {
//                                    dtoTemp.setStickObj(new HashMap<>());
//                                }
//
//                                dtoList.add(dtoTemp);
//                            }
//
//                            //maps.get(labelFeed.getFeedId()).add(id);
//                            FeedInfoDto dto = dtoMaps.get(labelFeed.getFeedId());
//
//                            dto.setLastId(id);
//                            innerLastId = id;
//                            feedNoteBuService.fillUserInfo(feedNote.getCreateuserid(), dto);
//
//                            // 大图片统计信息
//                            PicDto picDto = feedNoteBuService.getPicDto(feedNote.getId(), userId, labelFeed.getPic());
//                            FeedStatistics feedStatistics = feedStatisticsBuService.getFeedStatistics(feedNote.getId());
//                            picDto.setLikeNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getLikeVo(), flag));
//                            picDto.setDownLoadNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getDownLoadVo(), flag));
//                            picDto.setPrintNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getPrintVo(), flag));
//                            dto.setLikeNum(dto.getLikeNum() + picDto.getLikeNum());
//                            dto.setDownLoadNum(dto.getDownLoadNum() + picDto.getDownLoadNum());
//                            dto.setPrintNum(dto.getPrintNum() + picDto.getPrintNum());
//                            dto.getPicDto().add(picDto);
//                            dto.setShareNum(feedStatistics.getShareNum());
//                            dto.setGiveLikeNum(feedStatistics.getGiveLikeNum());
//
//                            // 小图
//                            PicDto smallPicDto = new PicDto();
//                            ToolsKit.Bean.copyProperties(picDto, smallPicDto);
//                            if (picDto.getHeight() >= Constant.OSS_MAX_VALUE || picDto.getWidth() >= Constant.OSS_MAX_VALUE) {
//                                smallPicDto.setPic(picDto.getPic());
//                            } else {
//                                smallPicDto.setPic(picDto.getPic() + Constant.FEED_SUFFIX);
//                            }
//                            dto.getSmallPicDto().add(smallPicDto);
//
//                            dto.setLabelMap(feedNoteBuService.getLabelMap(feedNote.getLabelIds()));
//                            feedNoteBuService.fillStatisticsNum(feedStatistics, labelFeed.getPic(), dto);
//                            dto.setContent(feedNote.getContent());
//                            dto.setTitle(feedNote.getTitle());
//                            dto.setFeedId(feedNote.getId());
//                            dto.setCreateTime(feedNote.getCreatetime());
//                            if (ToolsKit.isNotEmpty(feedNote.getHtmlUrl())) {
//                                dto.setHtmlUrl(feedNote.getHtmlUrl());
//                            } else {
//                                dto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.FEED_INFO_URL + "?feedid="
//                                        + feedNote.getId());
//                            }
//                            dto.setShareUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.SHAREVIEW_FEED_INFO_URL + "?feedid=" + dto.getFeedId());
//
//                            // 此处，发送兴趣圈的话，subject和gradeLevel都不会有值,处理发学习圈的
//                            if (ToolsKit.isNotEmpty(feedNote.getLabelIds()) && feedNote.getLabelIds().contains(studyLabelId)) {
//                                dto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.STUDY_NOTE_INFO_URL + "?feedid=" + feedNote.getId());
//                                dto.setShareUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.SHAREVIEW_STUDY_NOTE_INFO_URL + "?feedid=" + feedNote.getId());
//                            }
//
//                            dto.setIsFriend(FriendsStatusTypeEnums.NO_FRIEND.getValue());
////                                System.out.println("----------------------------------------------------------" + giveLikeFeedBuService.hasGiveLikeFeed(feedNote.getId(), userId));
//                            //增加是否已点赞
//                            if (giveLikeFeedBuService.hasGiveLikeFeed(feedNote.getId(), userId)) {
//                                dto.setHadGivedLike(1);
//                            } else {
//                                dto.setHadGivedLike(0);
//                            }
//                            //添加关注状态
//                            dto.setIsFollow(friendsFollowService.getFollowStatus(userId, feedNote.getCreateuserid()));
//                        }
//                    }
//                }
//            }
            if (ToolsKit.isNotEmpty(idsList)) {
                for (String id : idsList) {
                    LabelFeed labelFeed = this.getLabelFeedById(id);
                    if (ToolsKit.isNotEmpty(labelFeed)) {
                        FeedNote feedNote = feedNoteBuService.getFeedNoteById(labelFeed.getFeedId());
                        if (ToolsKit.isNotEmpty(feedNote)) {
                            // 置顶记录过滤掉先
                            if (feedNote.getStickNum() == FeedNoteStickEnums.TOP.getKey()) {
                                continue;
                            }
                            String tmpPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), labelFeed.getPic().getPic());
                            String flag = SecureUtil.md5(tmpPic);
                            if (checkPics.contains(flag)) {
                                // 图片已添加过
                                continue;
                            }
                            checkPics.add(flag);

                            if (!dtoMaps.containsKey(labelFeed.getFeedId())) {
                                if (dtoList.size() >= pageSize) {
                                    break;
                                }

                                //maps.put(labelFeed.getFeedId(), new ArrayList<>());
                                FeedInfoDto dtoTemp = new FeedInfoDto();
                                dtoMaps.put(labelFeed.getFeedId(), dtoTemp);
                                dtoTemp.setPicDto(new ArrayList<PicDto>());
                                dtoTemp.setSmallPicDto(new ArrayList<PicDto>());

                                // 设置精华标志
                                if (feedNote.getStickNum() != FeedNoteStickEnums.NONE.getKey()) {
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("key", feedNote.getStickNum());
                                    map.put("name", FeedNoteStickEnums.getMap().get(feedNote.getStickNum()).getDesc());
                                    map.put("icon", ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), FeedNoteStickEnums.getMap().get(feedNote.getStickNum()).getIcon()));
                                    dtoTemp.setStickObj(map);
                                } else {
                                    dtoTemp.setStickObj(new HashMap<>());
                                }

                                dtoList.add(dtoTemp);
                            }

                            //maps.get(labelFeed.getFeedId()).add(id);
                            FeedInfoDto dto = dtoMaps.get(labelFeed.getFeedId());

                            dto.setLastId(id);
//                            innerLastId = id;
                            feedNoteBuService.fillUserInfo(feedNote.getCreateuserid(), dto);

                            // 大图片统计信息
                            PicDto picDto = feedNoteBuService.getPicDto(feedNote.getId(), userId, labelFeed.getPic());
                            FeedStatistics feedStatistics = feedStatisticsBuService.getFeedStatistics(feedNote.getId());
                            picDto.setLikeNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getLikeVo(), flag));
                            picDto.setDownLoadNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getDownLoadVo(), flag));
                            picDto.setPrintNum(feedNoteBuService.getOneStatisticsNum(feedStatistics.getPrintVo(), flag));
                            dto.setLikeNum(dto.getLikeNum() + picDto.getLikeNum());
                            dto.setDownLoadNum(dto.getDownLoadNum() + picDto.getDownLoadNum());
                            dto.setPrintNum(dto.getPrintNum() + picDto.getPrintNum());
                            dto.getPicDto().add(picDto);
                            dto.setShareNum(feedStatistics.getShareNum());
                            dto.setGiveLikeNum(feedStatistics.getGiveLikeNum());

                            // 小图
                            PicDto smallPicDto = new PicDto();
                            ToolsKit.Bean.copyProperties(picDto, smallPicDto);
                            if (picDto.getHeight() >= Constant.OSS_MAX_VALUE || picDto.getWidth() >= Constant.OSS_MAX_VALUE) {
                                smallPicDto.setPic(picDto.getPic());
                            } else {
                                smallPicDto.setPic(picDto.getPic() + Constant.FEED_SUFFIX);
                            }
                            dto.getSmallPicDto().add(smallPicDto);

                            dto.setLabelMap(feedNoteBuService.getLabelMap(feedNote.getLabelIds()));
                            feedNoteBuService.fillStatisticsNum(feedStatistics, labelFeed.getPic(), dto);
                            dto.setContent(feedNote.getContent());
                            dto.setTitle(feedNote.getTitle());
                            dto.setFeedId(feedNote.getId());
                            dto.setCreateTime(feedNote.getCreatetime());
                            if (ToolsKit.isNotEmpty(feedNote.getHtmlUrl())) {
                                dto.setHtmlUrl(feedNote.getHtmlUrl());
                            } else {
                                dto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.FEED_INFO_URL + "?feedid="
                                        + feedNote.getId());
                            }
                            dto.setShareUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.SHAREVIEW_FEED_INFO_URL + "?feedid=" + dto.getFeedId());

                            // 此处，发送兴趣圈的话，subject和gradeLevel都不会有值,处理发学习圈的
                            if (ToolsKit.isNotEmpty(feedNote.getLabelIds()) && feedNote.getLabelIds().contains(studyLabelId)) {
                                dto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.STUDY_NOTE_INFO_URL + "?feedid=" + feedNote.getId());
                                dto.setShareUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.SHAREVIEW_STUDY_NOTE_INFO_URL + "?feedid=" + feedNote.getId());
                            }
                            dto.setIsFollow(0);
                            dto.setIsFriend(0);
//                            dto.setIsFriend(FriendsStatusTypeEnums.NO_FRIEND.getValue());
//                                System.out.println("----------------------------------------------------------" + giveLikeFeedBuService.hasGiveLikeFeed(feedNote.getId(), userId));
                            //增加是否已点赞
                            if (giveLikeFeedBuService.hasGiveLikeFeed(feedNote.getId(), userId)) {
                                dto.setHadGivedLike(1);
                            } else {
                                dto.setHadGivedLike(0);
                            }
                            //添加关注状态
//                            dto.setIsFollow(friendsFollowService.getFollowStatus(userId, feedNote.getCreateuserid()));
                        }
                    }
                }
            }
            for (FeedInfoDto dtoItem : dtoList) {
                List<PicDto> picList = new ArrayList<>();
                if (dtoItem.getPicDto() != null) {
                    for (int i = dtoItem.getPicDto().size() - 1; i >= 0; i--) {
                        picList.add(dtoItem.getPicDto().get(i));
                    }
                    dtoItem.getPicDto().clear();
                    dtoItem.getPicDto().addAll(picList);
                }

                List<PicDto> smallPicList = new ArrayList<>();
                if (dtoItem.getSmallPicDto() != null) {
                    for (int i = dtoItem.getSmallPicDto().size() - 1; i >= 0; i--) {
                        smallPicList.add(dtoItem.getSmallPicDto().get(i));
                    }
                    dtoItem.getSmallPicDto().clear();
                    dtoItem.getSmallPicDto().addAll(smallPicList);
                }
            }

            // 获取置顶信息，是否存在当前labelId
            // 如果是第一页，则添加置顶的数据，如果有的话
            if (pageNo == 0) {
                List<String> list = feedNoteCacheService.getTopFeedNoteList();
                if (ToolsKit.isNotEmpty(list)) {
                    for (String id : list) {
                        FeedInfoDto feedInfoDto = feedNoteBuService.getFeedInfoDto(userId, id, true, false, new ArrayList<>());
                        FeedNote feedNote = feedNoteBuService.getFeedNoteById(id);
                        if (!feedNote.getLabelIds().contains(labelId)) {
                            continue;
                        }
                        if (ToolsKit.isNotEmpty(feedInfoDto)) {
                            dtoList.add(0, feedInfoDto);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }

    /**
     * 根据ID获取标签动态对象
     *
     * @param id ID
     * @return 标签动态对象
     * @throws ServiceException
     */
    public LabelFeed getLabelFeedById(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        LabelFeed labelFeed = labelFeedCacheService.getLabelFeedById(id);
        if (ToolsKit.isEmpty(labelFeed)) {
            try {
                labelFeed = labelFeedDao.getLabelFeedById(id);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(labelFeed)) {
                labelFeedCacheService.saveLabelFeed(labelFeed);
            }
        }
        return labelFeed;
    }

    /**
     * 根据flag获取标签动态对象
     *
     * @param flag 标识
     * @return 标签动态对象
     * @throws ServiceException
     */
    public LabelFeed getLabelFeedByFlag(String labelId, String feedId, String flag, int type) throws ServiceException {
        // if (ToolsKit.isEmpty(labelId)) {
        // throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标签ID不能为空");
        // }
        if (ToolsKit.isEmpty(feedId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("动态ID不能为空");
        }
        if (ToolsKit.isEmpty(flag)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标识不能为空");
        }
        LabelFeed labelFeed = labelFeedCacheService.getLabelFeedByFlag(labelId, feedId, flag, type);
        if (ToolsKit.isEmpty(labelFeed)) {
            try {
                labelFeed = labelFeedDao.getLabelFeedByFlag(labelId, feedId, flag, type);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(labelFeed)) {
                labelFeedCacheService.saveLabelFeed(labelFeed);
            }
        }
        return labelFeed;
    }

    /**
     * 添加标签动态
     *
     * @param feedId  动态ID
     * @param labelId 标签ID
     * @param pic     图片地址
     * @throws ServiceException
     */
    public void addLabelFeed(String feedId, String labelId, String pic) throws ServiceException {
        if (ToolsKit.isEmpty(feedId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("动态ID不能为空");
        }
        // if (ToolsKit.isEmpty(labelId)) {
        // throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("标签ID不能为空");
        // }
        if (ToolsKit.isEmpty(pic)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("图片地址不能为空");
        }
        PicInfoDto picInfoDto = WebKit.getPicInfo(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), pic));
        String feedPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), pic);
        String flag = SecureUtil.md5(feedPic);
        int type = ToolsKit.isEmpty(labelId) || labelId.length() < 10 ? LabelTypeEnums.ALL.getValue() : LabelTypeEnums.LABEL.getValue();
        LabelFeed labelFeed = this.getLabelFeedByFlag(labelId, feedId, flag, type);
        if (ToolsKit.isNotEmpty(labelFeed)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("已添加过该记录");
        }
        if (!labelFeedCacheService.existsList(labelId)) {
            this.initLabelFeedList(labelId, type);
        }
        labelFeed = new LabelFeed();
        ToolsKit.setIdEntityData(labelFeed, ToolsConst.SYSTEM_USER_ID);
        labelFeed.setFeedId(feedId);
        labelFeed.setLabelId(labelId);
        labelFeed.setType(type);
        PicVo picVo = new PicVo();
        picVo.setPic(feedPic);
        if (ToolsKit.isNotEmpty(picInfoDto)) {
            picVo.setHeight(picInfoDto.getImageHeight());
            picVo.setWidth(picInfoDto.getImageWidth());
        }
        labelFeed.setPic(picVo);
        labelFeed.setFlag(flag);
        this.save(labelFeed);
        labelFeedCacheService.addLabelFeedIdToList(labelId, labelFeed.getId(), labelFeed.getCreatetime().getTime());
    }

    /**
     * 删除标签动态
     *
     * @param id 记录ID
     * @throws ServiceException
     */
    public void delLabelFeed(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录ID不能为空");
        }
        LabelFeed labelFeed = this.getLabelFeedById(id);
        if (ToolsKit.isNotEmpty(labelFeed)) {
            labelFeed.setStatus(ToolsConst.DATA_DELETE_STATUS);
            labelFeedDao.saveEntity(labelFeed);
            labelFeedCacheService.removeLabelFeedById(labelFeed.getId());
            labelFeedCacheService.removeLabelFeedByFlag(labelFeed.getLabelId(), labelFeed.getFeedId(), labelFeed.getFlag(), labelFeed.getType());
            labelFeedCacheService.removeLabelFeedIdToList(labelFeed.getLabelId(), labelFeed.getId());
        }
    }

    /**
     * 删除标签动态
     *
     * @param feedId 记录ID
     * @throws ServiceException
     */
    public void delLabelFeedByFeedId(String feedId) throws ServiceException {
        if (ToolsKit.isEmpty(feedId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("动态ID不能为空");
        }
        List<LabelFeed> list = labelFeedDao.findLabelFeedByFeedId(feedId);
        if (ToolsKit.isNotEmpty(list)) {
            for (LabelFeed labelFeed : list) {
                this.delLabelFeed(labelFeed.getId());
            }
        }
    }

    public List<FeedInfoDto> getLabelFeedListV400ForStudy(String keyword, String gradeLevel, String subject, String userId, int pageNo, int pageSize, String lastId, String version) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }

        // 获取labelId中，是“学习”的
        String labelId = labelBuService.getLabelListStudy();

        List<FeedInfoDto> dtoList = new ArrayList<FeedInfoDto>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            if (ToolsKit.isEmpty(labelId)) {
                labelId = StringUtils.EMPTY;
            }

            Page<FeedNote> feedNotePage = feedNoteBuService.findListPageByLabelId(pageNo, pageSize, labelId, null, subject, gradeLevel, keyword);
            List<FeedNote> feedNoteList = feedNotePage.getResult();
            if (ToolsKit.isNotEmpty(feedNoteList)) {
                System.out.println(feedNotePage.getResult().size());
                for (FeedNote temp : feedNoteList) {
                    FeedNote feedNote = feedNoteBuService.getFeedNoteById(temp.getId());
                    if (feedNote.getStickNum() == FeedNoteStickEnums.TOP.getKey() && ToolsKit.isEmpty(keyword)) {
                        continue;
                    }
                    FeedInfoDto dto = feedNoteBuService.getFeedNote(userId, temp.getId());
                    dtoList.add(dto);
                }
            }

            // 获取置顶信息，是否存在当前labelId
            // 如果是第一页，则添加置顶的数据，如果有的话
            // 搜索界面的话，取消此置顶功能
            if (pageNo == 0 && ToolsKit.isEmpty(keyword)) {
                List<String> list = feedNoteCacheService.getTopFeedNoteList();
                if (ToolsKit.isNotEmpty(list)) {
                    for (String id : list) {
                        FeedInfoDto feedInfoDto = feedNoteBuService.getFeedInfoDto(userId, id, true, false, new ArrayList<>());
                        FeedNote feedNote = feedNoteBuService.getFeedNoteById(id);
                        if (!feedNote.getLabelIds().contains(labelId)) {
                            continue;
                        }

                        // 还需过滤条件筛选
                        if (ToolsKit.isNotEmpty(keyword)) {
                            if (ToolsKit.isNotEmpty(feedNote.getKeyword()) || !feedNote.getKeyword().contains(keyword)) {
                                continue;
                            }
                        }

                        if (ToolsKit.isNotEmpty(subject)) {
                            if (!subject.equals(feedNote.getSubject())) {
                                continue;
                            }
                        }

                        if (ToolsKit.isNotEmpty(gradeLevel)) {
                            if (!subject.equals(feedNote.getGradeLevel())) {
                                continue;
                            }
                        }

                        if (ToolsKit.isNotEmpty(feedInfoDto)) {
                            feedInfoDto.setLastId(id);
                            feedInfoDto.setHtmlUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.STUDY_NOTE_INFO_URL + "?feedid=" + id);
                            feedInfoDto.setShareUrl(commonProperties.getFileDomain() + commonProperties.getH5Env() + Constant.SHAREVIEW_STUDY_NOTE_INFO_URL + "?feedid=" + feedInfoDto.getFeedId());
                            dtoList.add(0, feedInfoDto);
                        }
                    }
                }
            }

//            for (FeedInfoDto dtoItem : dtoList) {
//                List<PicDto> picList = new ArrayList<>();
//                if (dtoItem.getPicDto() != null) {
//                    for (int i = dtoItem.getPicDto().size() - 1; i >= 0; i--) {
//                        picList.add(dtoItem.getPicDto().get(i));
//                    }
//                    dtoItem.getPicDto().clear();
//                    dtoItem.getPicDto().addAll(picList);
//                }
//
//                List<PicDto> smallPicList = new ArrayList<>();
//                if (dtoItem.getSmallPicDto() != null) {
//                    for (int i = dtoItem.getSmallPicDto().size() - 1; i >= 0; i--) {
//                        smallPicList.add(dtoItem.getSmallPicDto().get(i));
//                    }
//                    dtoItem.getSmallPicDto().clear();
//                    dtoItem.getSmallPicDto().addAll(smallPicList);
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }
}
