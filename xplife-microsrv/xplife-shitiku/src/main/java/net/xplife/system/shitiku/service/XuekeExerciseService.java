package net.xplife.system.shitiku.service;

import net.xplife.system.shitiku.dto.*;
import net.xplife.system.shitiku.dto.xueke.*;
import net.xplife.system.shitiku.enums.XuekeSubjectTypeEnum;
import net.xplife.system.shitiku.util.ThirdPartyCallUtil;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.web.config.CommonProperties;
import net.xplife.system.shitiku.util.TextBookNameParseUtil;
import net.xplife.system.shitiku.util.DataConversionUtil;
import net.xplife.system.tools.util.core.ToolsKit;

import net.xplife.system.shitiku.dao.XueKeTextBookCacheDao;
import net.xplife.system.shitiku.entity.XueKeTextBookCache;
import net.xplife.system.shitiku.service.XuekeApiService;
import net.xplife.system.shitiku.enums.XueKeWangAddressEnums;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.cache.kit.CacheKit;
import net.xplife.system.tools.common.exception.ServiceException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 学科网练习相关服务类
 * 专门为XuekeExerciseController提供服务，使用全品接口格式
 * 完全独立，不依赖XuekeBuService
 */
@Service
public class XuekeExerciseService {

    @Autowired
    private XuekeApiService apiService;

    @Autowired
    private XueKeTextBookCacheDao xueKeTextBookCacheDao;

    @Autowired
    private CommonProperties commonProperties;

    @Autowired
    private ThirdPartyCallUtil thirdPartyCallUtil;

    private static final String CALL_BACK_CACHE_URL_KEY = "%s。%s。%s。%s。"; // paperIdEnc,paperVersion,paperSize,paperType

    public String paperDownload(String paperId, Boolean with_answer, Boolean with_stem, String client_user_id, String file_name, Boolean with_exp, Integer answer_position, Boolean with_answer_area, String file_format) {
        String result = apiService.paperDownload(paperId, with_answer, with_stem, client_user_id, file_name, with_exp, answer_position, with_answer_area, file_format);
        JSONObject data = getData(result);
        if (!ToolsKit.isEmpty(data)) {
            return data.getString("url");
        }
        return null;
//        this.settingPaperDownloadCallBack(url, paperId, with_answer, with_stem, client_user_id, file_name, with_exp, answer_position, with_answer_area, file_format);
    }


    public void settingPaperDownloadCallBack(String file, String paperId, Boolean with_answer, Boolean with_stem, String client_user_id, String file_name, Boolean with_exp, Integer answer_position, Boolean with_answer_area, String file_format) {
        // 放到redis缓存中，持续30分钟
        String cacheKey = String.format(CALL_BACK_CACHE_URL_KEY, paperId, with_answer, with_stem, client_user_id, file_name, with_exp, answer_position, with_answer_area, file_format);
        CacheKit.cache().set(cacheKey, file, ToolsConst.THIRTY_MINUTES);
    }

    public String paperDownloadByPdfUrl(String paperId, Boolean with_answer, Boolean with_stem, String client_user_id, String file_name, Boolean with_exp, Integer answer_position, Boolean with_answer_area, String file_format) {
        String cacheKey = String.format(CALL_BACK_CACHE_URL_KEY, paperId, with_answer, with_stem, client_user_id, file_name, with_exp, answer_position, with_answer_area, file_format);
        if (CacheKit.cache().exists(cacheKey)) {
            return CacheKit.cache().get(cacheKey, String.class);
        }
        return "";
    }

    /**
     * 获取指定ID的行政区
     *
     * @param id
     * @return
     */
    public AreasResponseData areasById(String id) {
        String key = String.format(XueKeWangAddressEnums.AREAS.getCacheKey(), id);
        String response = "";
        if (CacheKit.cache().exists(key)) {
            response = CacheKit.cache().get(key, String.class);
        } else {
            response = apiService.areas(id);
        }

        AreasByIdResponseVO repo = JSONObject.parseObject(response, AreasByIdResponseVO.class);
        checkResponse(repo, response, key);
        return repo.getData();
    }

    /***
     * 获取行政区列表
     * @return
     */
    public List<AreasResponseData> areasAll() {
//        List<AreasResponseData> areasResponseData = new ArrayList<>();
//        String response = "";
//        AreasResponseVO repo = new AreasResponseVO();
//        String key = XueKeWangAddressEnums.AREAS_ALL_PAPER.getCacheKey();
//        if (CacheKit.cache().exists(key)) {
//            areasResponseData = CacheKit.cache().getArray(key, AreasResponseData.class);
//        } else {
//            response = apiService.areasAll();
//            repo = JSONObject.parseObject(response, AreasResponseVO.class);
//            areasResponseData = repo.getData().stream().filter(item -> item.getLevel().equals("PROVINCE")).collect(Collectors.toList());
//            CacheKit.cache().set(key, areasResponseData, ToolsConst.MONTH_SECOND);
//        }
//        checkResponse(areasResponseData, null, null);
//        return areasResponseData;
        String response = "";
        String key = XueKeWangAddressEnums.AREAS_ALL.getCacheKey();
        if (CacheKit.cache().exists(key)) {
            response = CacheKit.cache().get(key, String.class);
        } else {
            response = apiService.areasAll();
            CacheKit.cache().set(key, response, ToolsConst.MONTH_SECOND);
        }
        AreasResponseVO repo = JSONObject.parseObject(response, AreasResponseVO.class);
        checkResponse(repo, null, null);
        return repo.getData().stream().filter(item -> item.getLevel().equals("PROVINCE")).collect(Collectors.toList());
    }

    /**
     * 获取所有学段和学科列表（不需要参数）
     *
     * @return 所有学段学科列表（QuanPin格式：按学段分组）
     */
    public List<Object> getAllSubjects() {
        List<Object> result = new ArrayList<>();

        try {
            // 从Xueke API获取所有学段和学科数据，并按QuanPin格式分组
            result = getAllSubjectsGroupedByPeriod();

        } catch (Exception e) {
            System.out.println("获取所有学科列表异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return result;
    }

    /**
     * 获取版本列表（参考textbooks接口参数）
     *
     * @param courseId  课程ID
     * @param gradeId   年级ID
     * @param pageIndex 当前页码（从1开始）
     * @param pageSize  每页数据条数
     * @param versionId 教材版本ID
     * @param isUpgrade 是否只取年级最后一个年级，因为升学， 0不是，1是
     * @return 版本列表（QuanPin格式）
     */
    public List<VersionDto> getVersions(String courseId, String gradeId, String pageIndex, String pageSize, String versionId, String isUpgrade) {
        List<VersionDto> result = new ArrayList<>();

        try {
            // 参数验证
            if (ToolsKit.isEmpty(courseId)) {
                return result;
            }

            // 设置默认值
            if (ToolsKit.isEmpty(pageIndex)) pageIndex = "1";
            if (ToolsKit.isEmpty(pageSize)) pageSize = "50";

            // 获取教材列表数据
            result = this.getVersionsFromTextbooks(courseId, gradeId, pageIndex, pageSize, versionId, isUpgrade);

        } catch (Exception e) {
            System.out.println("版本数据转换异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return result;
    }

    /**
     * 获取章节列表（转换为QuanPin格式）
     *
     * @param textbookId 教材ID
     * @return 章节列表（QuanPin格式）
     */
    public Object getChapterList(String textbookId, String chapterCode, String courseId, String versionCode, String gradeCode, String stageId) {
        try {
            // 参数验证
            if (ToolsKit.isEmpty(textbookId)) {
                return new HashMap<>();
            }

            // 使用缓存提高性能
            String cacheKey = "xueke:textboodId:" + textbookId + "&chapters:" + chapterCode + "$courseId" + courseId + "$versionCode" + versionCode + "$gradeCode" + gradeCode + "&stageId" + stageId;
            Object cachedResult = CacheKit.cache().get(cacheKey, Object.class);

            if (cachedResult != null) {
                return cachedResult;
            }

            // 调用CATALOG接口获取原始章节数据
            List<Object> catalogData = this.getTextbookCatalogDirect(textbookId);

            // 转换为QuanPin格式
            Object result = convertToQuanPinChapterFormat(catalogData, textbookId);
            //获取同步练习试卷类型
            List<PaperTypeDto> paperTypeHomeworkList = this.getPaperTypeHomeworkList(stageId, chapterCode);
            String type = "";
            if (!paperTypeHomeworkList.isEmpty()) {
                type = paperTypeHomeworkList.stream().filter(item -> "全部".equals(item.getName())).findFirst().get().getCode();
            }
            result = validateItems(getChapterList(result, chapterCode), courseId, versionCode, gradeCode, type);
            // 缓存结果（缓存4小时）
            if (result != null) {
                CacheKit.cache().set(cacheKey, result, ToolsConst.DAY_SECOND / 6);
            }

            return result;

        } catch (Exception e) {
            System.out.println("获取章节列表异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
//            return new HashMap<>();
        }
    }

    /**
     * 获取升学考试类型列表（QuanPin格式）
     *
     * @param subjectCode 学科代码
     * @return 升学考试类型列表（QuanPin格式）
     */
    public List<PaperTypeDto> getPaperTypeElevenPlusList(String subjectCode) {
        try {
            String cacheKey = "xueke:papertype:eleven:" + subjectCode;
            List<PaperTypeDto> cachedResult = CacheKit.cache().getArray(cacheKey, PaperTypeDto.class);

            if (cachedResult != null && !cachedResult.isEmpty()) {
                return cachedResult;
            }

            List<XuekePaperTypeDto> xuekeTypes = this.findPaperTypeListForElevenPlus();
            List<PaperTypeDto> result = convertToQuanPinPaperTypeFormat(xuekeTypes, subjectCode);

            // 缓存结果（缓存2小时）
            if (!result.isEmpty()) {
                CacheKit.cache().set(cacheKey, result, ToolsConst.DAY_SECOND / 12);
            }

            return result;
        } catch (Exception e) {
            System.out.println("获取升学考试类型异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
//            return new ArrayList<>();
        }
    }

    /**
     * 获取阶段考试类型列表（QuanPin格式）
     *
     * @param subjectCode 学科代码
     * @return 阶段考试类型列表（QuanPin格式）
     */
    public List<PaperTypeDto> getPaperTypeSectionalExamList(String subjectCode) {
        try {
            String cacheKey = "xueke:papertype:sectional:" + subjectCode;
            List<PaperTypeDto> cachedResult = CacheKit.cache().getArray(cacheKey, PaperTypeDto.class);

            if (cachedResult != null && !cachedResult.isEmpty()) {
                return cachedResult;
            }

            List<XuekePaperTypeDto> xuekeTypes = this.findPaperTypeListForSectionalExamination();
            List<PaperTypeDto> result = convertToQuanPinPaperTypeFormat(xuekeTypes, subjectCode);

            // 缓存结果（缓存2小时）
            if (!result.isEmpty()) {
                CacheKit.cache().set(cacheKey, result, ToolsConst.DAY_SECOND / 12);
            }

            return result;
        } catch (Exception e) {
            System.out.println("获取阶段考试类型异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
//            return new ArrayList<>();
        }
    }

    /**
     * 获取作业类型列表（QuanPin格式）
     *
     * @param subjectCode 学科代码
     * @return 作业类型列表（QuanPin格式）
     */
    public List<PaperTypeDto> getPaperTypeHomeworkList(String subjectCode, String chapterCode) {
        try {
            String cacheKey = "xueke:papertype:homework:" + subjectCode;
            List<PaperTypeDto> cachedResult = CacheKit.cache().getArray(cacheKey, PaperTypeDto.class);

            if (cachedResult != null && !cachedResult.isEmpty()) {
//                return filterPaperType(cachedResult, chapterCode);
                return cachedResult;
            }

            List<XuekePaperTypeDto> xuekeTypes = this.findPaperTypeListForHomework();
            List<PaperTypeDto> result = convertToQuanPinPaperTypeFormat(xuekeTypes, subjectCode);
            //只返回全部，不返回其他的类型
            result = result.stream().filter(item -> item.getName().equals("全部")).collect(Collectors.toList());

            // 缓存结果（缓存2小时）
            if (!result.isEmpty()) {
                CacheKit.cache().set(cacheKey, result, ToolsConst.DAY_SECOND / 12);
            }

//            return filterPaperType(result, chapterCode);
            return result;
        } catch (Exception e) {
            System.out.println("获取作业类型异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 过滤不存在试卷列表数据的分类
     *
     * @return
     * @Description //TODO
     * @Date 16:29
     * @Param
     **/
    private List<PaperTypeDto> filterPaperType(List<PaperTypeDto> paperTypeList, String chapterCode) {
        List<PaperTypeDto> result = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        int index = 0;
        for (PaperTypeDto paperTypeDto : paperTypeList) {
            if (paperTypeDto.getName().equals("全部")) {
                continue;
            }
            try {
                PaperListDto paperListDto = findPapersListQuanPinFormat(null, null, null, null, null,
                        chapterCode, paperTypeDto.getCode(), null, null, null, null);
                if (paperListDto != null && !paperListDto.getList().isEmpty()) {
                    result.add(paperTypeDto);
                    sb.append(paperTypeDto.getCode());
                    if (index != paperTypeList.size() - 1) {
                        sb.append(",");
                    }
                    index++;
                }
            } catch (Exception e) {
                System.out.println("获取试卷列表异常: " + e.getMessage());
            }

        }


        // 添加"全部"选项到列表第一位（参照QuanPin的做法）
        PaperTypeDto allOption = new PaperTypeDto();
        allOption.setCode(sb.length() > 0 ? sb.toString() : "");
        allOption.setName("全部");
        result.add(0, allOption);
        return result;
    }

    /**
     * 获取试卷列表（QuanPin格式）- 完全独立实现
     *
     * @param periodCode  学段代码（可选）
     * @param subjectCode 学科代码（可选）
     * @param paperName   试卷名称（可选）
     * @param versionCode 版本代码（可选）
     * @param gradeCode   年级代码（可选）
     * @param chapterCode 章节代码（可选）
     * @param type        试卷类型（可选）
     * @param year        年份（可选）
     * @param province    省份（可选）
     * @param currPage    当前页（可选，默认1）
     * @param pageSize    页大小（可选，默认50）
     * @return QuanPin格式的试卷列表
     */
    public PaperListDto findPapersListQuanPinFormat(String periodCode, String subjectCode, String paperName,
                                                    String versionCode, String gradeCode, String chapterCode,
                                                    String type, String year, String province,
                                                    Integer currPage, Integer pageSize) {
        try {
            // 设置默认值
            Integer finalCurrPage = (currPage != null && currPage > 0) ? currPage : 1;
            Integer finalPageSize = (pageSize != null && pageSize > 0) ? pageSize : 50;

            // 直接调用Xueke API获取试卷列表
            XuekePaperListDto xuekeResult = this.getPapersFromApi(
                    periodCode, subjectCode, paperName, versionCode, gradeCode,
                    chapterCode, type, year, province, finalCurrPage, finalPageSize);

            // 转换为QuanPin格式
            return convertToQuanPinPaperListFormat(xuekeResult);

        } catch (Exception e) {
            System.out.println("获取试卷列表异常: " + e.getMessage());
            throw e;
//            return new PaperListDto();
        }
    }

    /**
     * 获取试卷详情（QuanPin格式）
     *
     * @param formulaPicFormat 公式图片格式
     * @param clientUserId     客户端用户ID
     * @param paperId          试卷ID
     * @return QuanPin格式的试卷详情
     */
    public PaperDetailsDto findPaperDetailsQuanPinFormat(String formulaPicFormat, String clientUserId, String paperId) {
        try {
            // 参数验证
            if (ToolsKit.isEmpty(paperId)) {
                return new PaperDetailsDto();
            }

            // 调用Xueke API获取试卷详情
            // 如果没有指定格式，默认使用svg
            String format = ToolsKit.isNotEmpty(formulaPicFormat) ? formulaPicFormat : "svg";
            String response = apiService.paperPreview(format, clientUserId, paperId);

            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONObject dataObject = jsonObject.getJSONObject("data");
                    if (dataObject != null) {
                        // 转换为QuanPin格式
                        return convertToPaperDetailsDto(dataObject);
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("获取试卷详情异常: " + e.getMessage());
            e.printStackTrace();
        }

        return new PaperDetailsDto();
    }

    /**
     * 将XuekePaperTypeDto列表转换为QuanPin格式的PaperTypeDto列表
     *
     * @param xuekeTypes Xueke格式的试卷类型列表
     * @return QuanPin格式的试卷类型列表
     */
    private List<PaperTypeDto> convertToQuanPinPaperTypeFormat(List<XuekePaperTypeDto> xuekeTypes, String stageId) {
        List<PaperTypeDto> result = new ArrayList<>();

        if (xuekeTypes != null && !xuekeTypes.isEmpty()) {
            xuekeTypes = xuekeTypes.stream().filter(x -> x.getStageId().equals(Integer.parseInt(stageId))
                    || x.getStageId().equals(0)).collect(Collectors.toList());
            // 转换所有试卷类型
            StringBuilder codes = new StringBuilder();
            for (XuekePaperTypeDto xuekeType : xuekeTypes) {
                PaperTypeDto quanPinType = new PaperTypeDto();
                quanPinType.setName(xuekeType.getName());
                quanPinType.setCode(String.valueOf(xuekeType.getId())); // code值就是ID值
                result.add(quanPinType);

                // 收集所有code用于"全部"选项
                codes.append(xuekeType.getId()).append(",");
            }

            // 添加"全部"选项到列表第一位（参照QuanPin的做法）
            PaperTypeDto allOption = new PaperTypeDto();
            allOption.setCode(codes.toString());
            allOption.setName("全部");
            result.add(0, allOption);

        } else {
            // 如果没有数据，只返回"全部"选项
            PaperTypeDto allOption = new PaperTypeDto();
            allOption.setCode("");
            allOption.setName("全部");
            result.add(allOption);
        }

        return result;
    }

    /**
     * 将Xueke的XuekePaperListDto转换为QuanPin的PaperListDto格式（优化版）
     *
     * @param xuekeResult Xueke格式的试卷列表
     * @return QuanPin格式的试卷列表
     */
    private PaperListDto convertToQuanPinPaperListFormat(XuekePaperListDto xuekeResult) {
        PaperListDto result = new PaperListDto();

        if (xuekeResult != null) {
            // 转换分页信息
            result.setTotalCount(xuekeResult.getTotalSize());
            result.setTotalPage(xuekeResult.getTotalPage());
            result.setCurrPage(xuekeResult.getPageIndex());
            result.setPageSize(xuekeResult.getPageSize());

            // 转换试卷列表
            if (xuekeResult.getItems() != null && !xuekeResult.getItems().isEmpty()) {
                // 预加载当前页面需要的所有名称数据
                Map<String, String> nameCache = preloadNamesForPapers(xuekeResult.getItems());

                List<PaperInfo> paperInfos = new ArrayList<>();
                for (XuekePaperDto xuekePaper : xuekeResult.getItems()) {
                    PaperInfo paperInfo = convertXuekePaperToPaperInfo(xuekePaper, nameCache);
                    paperInfos.add(paperInfo);
                }
                result.setList(paperInfos);
            }
        }

        return result;
    }

    /**
     * 将单个XuekePaperDto转换为QuanPin的PaperInfo格式（完整保留版）
     *
     * @param xuekePaper Xueke格式的试卷
     * @param nameCache  预加载的名称缓存
     * @return QuanPin格式的试卷信息（包含所有原始字段）
     */
    private PaperInfo convertXuekePaperToPaperInfo(XuekePaperDto xuekePaper, Map<String, String> nameCache) {
        PaperInfo paperInfo = new PaperInfo();

        if (xuekePaper != null) {
            // ========== QuanPin标准字段映射 ==========

            // 基础信息映射
            paperInfo.setPaperId(ToolsKit.isNotEmpty(xuekePaper.getId()) ? Long.valueOf(xuekePaper.getId()) : null);
            paperInfo.setPaperIdEnc(xuekePaper.getId()); // 使用ID作为加密ID
            paperInfo.setPaperName(xuekePaper.getTitle());

            // 学段学科信息
            paperInfo.setPeriod(String.valueOf(xuekePaper.getStage_id()));
            paperInfo.setPeriodName(getStageNameById(xuekePaper.getStage_id()));
            paperInfo.setSubjectCode(String.valueOf(xuekePaper.getCourse_id()));

            // 从缓存获取课程名称，提高性能
            String courseKey = "course:" + xuekePaper.getCourse_id();
            paperInfo.setSubjectName(nameCache.getOrDefault(courseKey, "课程" + xuekePaper.getCourse_id()));

            // 年级信息
            paperInfo.setGrade(String.valueOf(xuekePaper.getGrade_id()));
            String gradeKey = "grade:" + xuekePaper.getGrade_id();
            paperInfo.setGradeName(nameCache.getOrDefault(gradeKey, getGradeNameFallback(xuekePaper.getGrade_id())));

            // 试卷类型
            paperInfo.setTypeCode(String.valueOf(xuekePaper.getType_id()));
            String typeKey = "type:" + xuekePaper.getType_id();
            paperInfo.setTypeName(nameCache.getOrDefault(typeKey, "类型" + xuekePaper.getType_id()));

            // 其他QuanPin标准信息
            paperInfo.setTotalCount(xuekePaper.getQuestion_count());
            paperInfo.setYear(String.valueOf(xuekePaper.getYear()));
            paperInfo.setStatus(1); // 默认已入库
            paperInfo.setSource(2); // 来源：学科网

            // 时间信息（需要转换格式）
            paperInfo.setTimeCreate(convertDateToTimestamp(xuekePaper.getCreate_date()));
            paperInfo.setTimeModified(convertDateToTimestamp(xuekePaper.getUpdate_date()));

            // 省份信息（取第一个地区ID）
            if (xuekePaper.getArea_ids() != null && xuekePaper.getArea_ids().length > 0) {
                paperInfo.setProvince(String.join(",", xuekePaper.getArea_ids()));
//                paperInfo.setProvince(xuekePaper.getArea_ids()[0]);
                // 这里可以添加省份名称查询逻辑
                paperInfo.setProvinceName(""); // 暂时为空，可以后续优化
            }

            // QuanPin默认值
            paperInfo.setTotalCountView(0);
            paperInfo.setTotalCountDownload(0);
            paperInfo.setIsCollected(0);
            paperInfo.setCreatorGuid(0L);

            // ========== Xueke独有字段保留（不与QuanPin重复） ==========

            // 只保留QuanPin中没有对应字段的Xueke原始字段
            paperInfo.setVersion_id(xuekePaper.getVersion_id()); // QuanPin没有版本ID字段
            paperInfo.setCatalog_ids(xuekePaper.getCatalog_ids()); // QuanPin没有章节ID数组字段
            paperInfo.setArea_ids(xuekePaper.getArea_ids()); // QuanPin只有单个province，这里保留完整地区数组
            paperInfo.setOrg_id(xuekePaper.getOrg_id()); // QuanPin没有机构ID字段
            paperInfo.setOrg_name(xuekePaper.getOrg_name()); // QuanPin没有机构名称字段
            paperInfo.setTerm(xuekePaper.getTerm()); // QuanPin没有学期字段
            paperInfo.setStage_id(xuekePaper.getStage_id());
            paperInfo.setCourse_id(xuekePaper.getCourse_id());
            paperInfo.setQuestion_count(xuekePaper.getQuestion_count());

            // Xueke独有的扩展字段
            paperInfo.setOrdinal(null); // 排序值，QuanPin没有此字段
            paperInfo.setDescription(null); // 试卷描述，QuanPin没有此字段
            paperInfo.setDifficulty(null); // 难度，QuanPin没有此字段
            paperInfo.setScore(null); // 试卷分数，QuanPin没有此字段
            paperInfo.setDuration(null); // 试卷时长，QuanPin没有此字段
            paperInfo.setPaper_status(null); // 试卷状态，QuanPin没有此字段
            paperInfo.setHas_audio(extractHasAudioFromTags(xuekePaper.getTags())); // 听力信息，QuanPin没有此字段
            paperInfo.setFormat(null); // 试卷格式，QuanPin没有此字段

            // 标签信息转换（Xueke独有字段）
            if (xuekePaper.getTags() != null) {
                List<Object> tags = new ArrayList<>();
                for (XuekeBaseDto tag : xuekePaper.getTags()) {
                    tags.add(tag);
                }
                paperInfo.setTags(tags);
            }

            // 等级信息转换（Xueke独有字段）
            if (xuekePaper.getLevels() != null) {
                List<Object> levels = new ArrayList<>();
                for (XuekeBaseDto level : xuekePaper.getLevels()) {
                    levels.add(level);
                }
                paperInfo.setLevels(levels);
            }

            // 保留完整原始数据
//            paperInfo.setOriginalData(xuekePaper);
        }

        return paperInfo;
    }

    /**
     * 年级名称降级处理（快速版本）
     *
     * @param gradeId 年级ID
     * @return 年级名称
     */
    private String getGradeNameFallback(Integer gradeId) {
        if (gradeId == null) return "";

        if (gradeId <= 6) {
            return gradeId + "年级";
        } else if (gradeId <= 9) {
            return "初" + (gradeId - 6) + "年级";
        } else if (gradeId <= 12) {
            return "高" + (gradeId - 9) + "年级";
        }
        return "年级" + gradeId;
    }

    /**
     * 直接从API获取试卷列表（完全独立实现）
     *
     * @param periodCode  学段代码
     * @param subjectCode 学科代码
     * @param paperName   试卷名称
     * @param versionCode 版本代码
     * @param gradeCode   年级代码
     * @param chapterCode 章节代码
     * @param type        试卷类型
     * @param year        年份
     * @param province    省份
     * @param currPage    当前页
     * @param pageSize    页大小
     * @return Xueke格式的试卷列表
     */
    private XuekePaperListDto getPapersFromApi(String periodCode, String subjectCode, String paperName,
                                               String versionCode, String gradeCode, String chapterCode,
                                               String type, String year, String province,
                                               Integer currPage, Integer pageSize) {

        // 参数映射：QuanPin → Xueke API
        String stageId = periodCode;
        String courseId = subjectCode;
        String titleKeyword = paperName;
        String[] versionIds = ToolsKit.isNotEmpty(versionCode) ? versionCode.split(",") : null;
        String gradeId = gradeCode;
        String[] catalogIds = ToolsKit.isNotEmpty(chapterCode) ? chapterCode.split(",") : null;
        String[] typeIds = ToolsKit.isNotEmpty(type) ? type.split(",") : null;
        String[] years = ToolsKit.isNotEmpty(year) ? year.split(",") : null;
        String areaId = province;
        Integer pageIndex = currPage != null ? currPage : 1;
        Integer pageSizeValue = pageSize != null ? pageSize : 50;

        // 直接调用Xueke API
        String response = apiService.getPapersData(
                stageId, titleKeyword, courseId, null, versionIds, gradeId, areaId,
                catalogIds, typeIds, years, null, null, null, null, pageIndex, pageSizeValue);

        // 解析API响应
        if (ToolsKit.isNotEmpty(response)) {
            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject.getInteger("code") == 2000000) {
                JSONObject dataObject = jsonObject.getJSONObject("data");
                if (dataObject != null) {
                    return JSON.parseObject(dataObject.toJSONString(), XuekePaperListDto.class);
                }
            } else {
                throw new ServiceException(jsonObject.getString("msg"), jsonObject.getInteger("code"));
            }
        }


        // 返回空结果
        XuekePaperListDto emptyResult = new XuekePaperListDto();
        emptyResult.setPageIndex(currPage != null ? currPage : 1);
        emptyResult.setPageSize(pageSize != null ? pageSize : 50);
        emptyResult.setTotalPage(0);
        emptyResult.setTotalSize(0);
        emptyResult.setItems(new ArrayList<>());
        return emptyResult;
    }

    /**
     * 为当前页面的试卷预加载所有需要的名称数据
     *
     * @param papers 试卷列表
     * @return 名称缓存Map
     */
    private Map<String, String> preloadNamesForPapers(List<XuekePaperDto> papers) {
        Map<String, String> nameCache = new HashMap<>();

        if (papers == null || papers.isEmpty()) {
            return nameCache;
        }

        // 收集所有需要查询的ID
        Set<Integer> courseIds = new HashSet<>();
        Set<Integer> gradeIds = new HashSet<>();
        Set<Integer> typeIds = new HashSet<>();

        for (XuekePaperDto paper : papers) {
            if (paper.getCourse_id() != null) courseIds.add(paper.getCourse_id());
            if (paper.getGrade_id() != null) gradeIds.add(paper.getGrade_id());
            if (paper.getType_id() != null) typeIds.add(paper.getType_id());
        }

        // 批量加载课程名称
        loadCourseNames(courseIds, nameCache);

        // 批量加载试卷类型名称
        loadTypeNames(typeIds, nameCache);

        // 年级名称使用降级处理（避免复杂的API调用）
        for (Integer gradeId : gradeIds) {
            String gradeKey = "grade:" + gradeId;
            nameCache.put(gradeKey, getGradeNameFallback(gradeId));
        }

        return nameCache;
    }

    /**
     * 将Xueke的试卷详情数据转换为QuanPin的PaperDetailsDto格式
     *
     * @param dataObject Xueke格式的试卷详情数据
     * @return QuanPin格式的试卷详情
     */
    private PaperDetailsDto convertToPaperDetailsDto(JSONObject dataObject) {
        PaperDetailsDto paperDetails = new PaperDetailsDto();

        try {
            // 基础信息映射
            paperDetails.setPaperName(dataObject.getString("title"));
            paperDetails.setPaperDesc(dataObject.getString("description"));
            paperDetails.setPaperTotalCount(String.valueOf(dataObject.getInteger("question_count")));

            // 试卷ID和基础信息
            paperDetails.setPaperId(dataObject.getString("id"));
            paperDetails.setCourseId(String.valueOf(dataObject.getInteger("course_id")));
            paperDetails.setYear(String.valueOf(dataObject.getInteger("year")));
            paperDetails.setVersionId(String.valueOf(dataObject.getInteger("version_id")));
            paperDetails.setTerm(dataObject.getString("term"));
            paperDetails.setOrgId(String.valueOf(dataObject.getInteger("org_id")));
            paperDetails.setOrgName(dataObject.getString("org_name"));

            // 试卷类型信息
            Integer typeId = dataObject.getInteger("type_id");
            if (typeId != null) {
                paperDetails.setPaperTypeCode(String.valueOf(typeId));
                paperDetails.setPaperType(getTypeNameById(typeId));
            }

            // 年级信息
            Integer gradeId = dataObject.getInteger("grade_id");
            if (gradeId != null) {
                paperDetails.setGrade(String.valueOf(gradeId));
                paperDetails.setGradeName(getGradeNameFallback(gradeId));
            }

            // 统计信息
            paperDetails.setDownCount("0"); // 默认值，Xueke可能没有这个字段
            paperDetails.setViewCount("0"); // 默认值

            // 来源信息
            paperDetails.setSource("2"); // 默认来源：学科网

            // 时间信息
            String updateDate = dataObject.getString("update_date");
            paperDetails.setTimeModified(convertDateToTimestamp(updateDate));

            // 创建者信息
            paperDetails.setCreatorGuid("0"); // 默认值

            // 地区信息
            JSONArray areaIdsArray = dataObject.getJSONArray("area_ids");
            if (areaIdsArray != null) {
                List<String> areaIds = new ArrayList<>();
                for (int i = 0; i < areaIdsArray.size(); i++) {
                    areaIds.add(String.valueOf(areaIdsArray.get(i)));
                }
                paperDetails.setAreaIds(areaIds);

                // 设置省份（取第一个地区ID）
                if (!areaIds.isEmpty()) {
                    paperDetails.setProvince(areaIds.get(0));
                }
            }

            // 章节信息
            JSONArray catalogIdsArray = dataObject.getJSONArray("catalog_ids");
            if (catalogIdsArray != null) {
                List<String> catalogIds = new ArrayList<>();
                for (int i = 0; i < catalogIdsArray.size(); i++) {
                    catalogIds.add(String.valueOf(catalogIdsArray.get(i)));
                }
                paperDetails.setCatalogIds(catalogIds);
            }

            // 标签信息
            JSONArray tagsArray = dataObject.getJSONArray("tags");
            if (tagsArray != null) {
                List<Object> tags = new ArrayList<>();
                for (int i = 0; i < tagsArray.size(); i++) {
                    tags.add(tagsArray.get(i));
                }
                paperDetails.setTags(tags);
            }

            // 保留原始HTML结构
            String paperStructHtml = dataObject.getString("paper_struct_html");
            paperDetails.setPaperStructHtml(paperStructHtml);

            // 试卷结构（简化版，用于兼容QuanPin）
            if (ToolsKit.isNotEmpty(paperStructHtml)) {
                List<PaperStructureDto> paperStructure = convertToPaperStructureFromHtml(paperStructHtml, null);
                paperDetails.setPaperStructure(paperStructure);
            }

            // 题目信息（保留完整数据）
            JSONArray questionsArray = dataObject.getJSONArray("questions");
            if (questionsArray != null) {
                List<QuestionDto> questions = convertToQuestionDtos(questionsArray);
                paperDetails.setQuestions(questions);
            }

        } catch (Exception e) {
            System.out.println("转换试卷详情格式异常: " + e.getMessage());
            e.printStackTrace();
        }

        return paperDetails;
    }

    /**
     * 从HTML结构和题目数组转换试卷结构数据
     *
     * @param paperStructHtml 试卷结构HTML
     * @param questionsArray  题目数组
     * @return QuanPin格式的试卷结构
     */
    private List<PaperStructureDto> convertToPaperStructureFromHtml(String paperStructHtml, JSONArray questionsArray) {
        List<PaperStructureDto> result = new ArrayList<>();

        try {
            // 解析HTML中的结构信息，提取大题分组
            List<String> sections = extractSectionsFromHtml(paperStructHtml);

            // 如果没有明确的分组，创建默认结构
            if (sections.isEmpty()) {
                sections.add("试题部分");
            }

            // 为每个分组创建结构
            for (int i = 0; i < sections.size(); i++) {
                PaperStructureDto structure = new PaperStructureDto();
                structure.setSegmentName(sections.get(i));
                structure.setName(sections.get(i));
                structure.setDesc(sections.get(i));
                structure.setSectionId((long) (i + 1));
                structure.setIsValid(1); // 显示

                result.add(structure);
            }

        } catch (Exception e) {
            System.out.println("转换试卷结构异常: " + e.getMessage());
            e.printStackTrace();

            // 降级处理：创建默认结构
            PaperStructureDto defaultStructure = new PaperStructureDto();
            defaultStructure.setSegmentName("试题部分");
            defaultStructure.setName("试题部分");
            defaultStructure.setDesc("试题部分");
            defaultStructure.setSectionId(1L);
            defaultStructure.setIsValid(1);
            result.add(defaultStructure);
        }

        return result;
    }

    /**
     * 从HTML中提取试卷分组信息
     *
     * @param paperStructHtml 试卷结构HTML
     * @return 分组名称列表
     */
    private List<String> extractSectionsFromHtml(String paperStructHtml) {
        List<String> sections = new ArrayList<>();

        if (ToolsKit.isEmpty(paperStructHtml)) {
            return sections;
        }

        try {
            // 从HTML中提取分组信息
            // 根据paper.txt的结构，可以看到有"基础知识"、"综合实践"等分组
            if (paperStructHtml.contains("基础知识")) {
                sections.add("基础知识");
            }
            if (paperStructHtml.contains("综合实践")) {
                sections.add("综合实践");
            }
            if (paperStructHtml.contains("单项选择")) {
                sections.add("单项选择");
            }

            // 如果没有找到明确的分组，尝试提取其他可能的分组
            if (sections.isEmpty()) {
                // 可以根据实际需要添加更多的分组识别逻辑
                if (paperStructHtml.contains("一、") || paperStructHtml.contains("二、") || paperStructHtml.contains("三、")) {
                    sections.add("第一部分");
                    if (paperStructHtml.contains("二、")) sections.add("第二部分");
                    if (paperStructHtml.contains("三、")) sections.add("第三部分");
                }
            }

        } catch (Exception e) {
            System.out.println("提取HTML分组异常: " + e.getMessage());
        }

        return sections;
    }

    /**
     * 将题目数组转换为QuestionDto列表（保留完整数据）
     *
     * @param questionsArray 题目JSON数组
     * @return QuestionDto列表
     */
    private List<QuestionDto> convertToQuestionDtos(JSONArray questionsArray) {
        List<QuestionDto> questions = new ArrayList<>();

        try {
            for (int i = 0; i < questionsArray.size(); i++) {
                JSONObject questionJson = questionsArray.getJSONObject(i);
                QuestionDto question = convertToQuestionDto(questionJson);
                questions.add(question);
            }
        } catch (Exception e) {
            System.out.println("转换题目数据异常: " + e.getMessage());
            e.printStackTrace();
        }

        return questions;
    }

    /**
     * 将单个题目JSON转换为QuestionDto（保留所有字段）
     *
     * @param questionJson 题目JSON对象
     * @return QuestionDto
     */
    private QuestionDto convertToQuestionDto(JSONObject questionJson) {
        QuestionDto question = new QuestionDto();

        try {
            // 基础字段
            question.setId(questionJson.getString("id"));
            question.setStem(questionJson.getString("stem"));
            question.setAnswer(questionJson.getString("answer"));
            question.setExplanation(questionJson.getString("explanation"));
            question.setType(questionJson.getString("type"));
            question.setTypeName(questionJson.getString("type_name"));

            // 数值字段
            question.setScore(questionJson.getDouble("score"));
            question.setSort(questionJson.getInteger("sort"));
            question.setDifficulty(questionJson.getString("difficulty"));

            // 时间字段
            question.setCreateTime(questionJson.getString("create_time"));
            question.setUpdateTime(questionJson.getString("update_time"));

            // 其他字段
            question.setSource(questionJson.getString("source"));
            question.setYear(questionJson.getString("year"));
            question.setArea(questionJson.getString("area"));
            question.setSchool(questionJson.getString("school"));

            // 数组字段
            JSONArray knowledgePointsArray = questionJson.getJSONArray("knowledge_points");
            if (knowledgePointsArray != null) {
                List<Object> knowledgePoints = new ArrayList<>();
                for (int i = 0; i < knowledgePointsArray.size(); i++) {
                    knowledgePoints.add(knowledgePointsArray.get(i));
                }
                question.setKnowledgePoints(knowledgePoints);
            }

            JSONArray optionsArray = questionJson.getJSONArray("options");
            if (optionsArray != null) {
                List<Object> options = new ArrayList<>();
                for (int i = 0; i < optionsArray.size(); i++) {
                    options.add(optionsArray.get(i));
                }
                question.setOptions(options);
            }

            JSONArray tagsArray = questionJson.getJSONArray("tags");
            if (tagsArray != null) {
                List<Object> tags = new ArrayList<>();
                for (int i = 0; i < tagsArray.size(); i++) {
                    tags.add(tagsArray.get(i));
                }
                question.setTags(tags);
            }

            JSONArray imageUrlsArray = questionJson.getJSONArray("image_urls");
            if (imageUrlsArray != null) {
                List<String> imageUrls = new ArrayList<>();
                for (int i = 0; i < imageUrlsArray.size(); i++) {
                    imageUrls.add(imageUrlsArray.getString(i));
                }
                question.setImageUrls(imageUrls);
            }

            // 子题目（如果有）
            JSONArray subQuestionsArray = questionJson.getJSONArray("sub_questions");
            if (subQuestionsArray != null) {
                List<QuestionDto> subQuestions = convertToQuestionDtos(subQuestionsArray);
                question.setSubQuestions(subQuestions);
            }

            // 保留原始数据
            question.setOriginalData(questionJson);

        } catch (Exception e) {
            System.out.println("转换单个题目异常: " + e.getMessage());
            // 即使转换失败，也保留原始数据
            question.setOriginalData(questionJson);
        }

        return question;
    }

    /**
     * 从Xueke的tags中提取是否包含听力信息
     *
     * @param tags 标签列表
     * @return 是否包含听力
     */
    private Boolean extractHasAudioFromTags(List<XuekeBaseDto> tags) {
        if (tags == null || tags.isEmpty()) {
            return false;
        }

        try {
            for (XuekeBaseDto tag : tags) {
                if (tag != null && tag.getName() != null) {
                    String tagName = tag.getName().toLowerCase();
                    // 听力标签：1 含听力题（带音频）、2 含听力题（无音频）、3 不含听力题
                    if (tagName.contains("听力") || tagName.contains("audio")) {
                        return !tagName.contains("不含") && !tagName.contains("无音频");
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("提取听力信息异常: " + e.getMessage());
        }

        return false;
    }

    /**
     * 批量加载课程名称
     *
     * @param courseIds 课程ID集合
     * @param nameCache 名称缓存
     */
    private void loadCourseNames(Set<Integer> courseIds, Map<String, String> nameCache) {
        if (courseIds.isEmpty()) return;

        try {
            // 先从缓存获取
            for (Integer courseId : courseIds) {
                String cacheKey = "xueke:course:name:" + courseId;
                String courseName = CacheKit.cache().get(cacheKey, String.class);
                if (ToolsKit.isNotEmpty(courseName)) {
                    nameCache.put("course:" + courseId, courseName);
                }
            }

            // 检查是否还有未缓存的课程
            Set<Integer> uncachedCourseIds = new HashSet<>();
            for (Integer courseId : courseIds) {
                if (!nameCache.containsKey("course:" + courseId)) {
                    uncachedCourseIds.add(courseId);
                }
            }

            // 如果有未缓存的课程，调用API批量获取
            if (!uncachedCourseIds.isEmpty()) {
                String response = apiService.coursesAll();
                if (ToolsKit.isNotEmpty(response)) {
                    JSONObject jsonObject = JSON.parseObject(response);
                    if (jsonObject.getInteger("code") == 2000000) {
                        JSONArray dataArray = jsonObject.getJSONArray("data");
                        if (dataArray != null) {
                            for (int i = 0; i < dataArray.size(); i++) {
                                JSONObject courseItem = dataArray.getJSONObject(i);
                                Integer id = courseItem.getInteger("id");
                                String name = courseItem.getString("name");

                                // 缓存到Redis
                                String cacheKey = "xueke:course:name:" + id;
                                CacheKit.cache().set(cacheKey, name, ToolsConst.DAY_SECOND);

                                // 如果是当前需要的课程，添加到本次缓存
                                if (uncachedCourseIds.contains(id)) {
                                    nameCache.put("course:" + id, name);
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("批量加载课程名称异常: " + e.getMessage());
            // 降级处理：为未获取到名称的课程设置默认值
            for (Integer courseId : courseIds) {
                if (!nameCache.containsKey("course:" + courseId)) {
                    nameCache.put("course:" + courseId, "课程" + courseId);
                }
            }
        }
    }

    /**
     * 批量加载试卷类型名称
     *
     * @param typeIds   类型ID集合
     * @param nameCache 名称缓存
     */
    private void loadTypeNames(Set<Integer> typeIds, Map<String, String> nameCache) {
        if (typeIds.isEmpty()) return;

        try {
            // 先从缓存获取
            for (Integer typeId : typeIds) {
                String cacheKey = "xueke:papertype:name:" + typeId;
                String typeName = CacheKit.cache().get(cacheKey, String.class);
                if (ToolsKit.isNotEmpty(typeName)) {
                    nameCache.put("type:" + typeId, typeName);
                }
            }

            // 检查是否还有未缓存的类型
            Set<Integer> uncachedTypeIds = new HashSet<>();
            for (Integer typeId : typeIds) {
                if (!nameCache.containsKey("type:" + typeId)) {
                    uncachedTypeIds.add(typeId);
                }
            }

            // 如果有未缓存的类型，调用API批量获取
            if (!uncachedTypeIds.isEmpty()) {
                String response = apiService.paperTypes();
                if (ToolsKit.isNotEmpty(response)) {
                    JSONObject jsonObject = JSON.parseObject(response);
                    if (jsonObject.getInteger("code") == 2000000) {
                        JSONArray dataArray = jsonObject.getJSONArray("data");
                        if (dataArray != null) {
                            for (int i = 0; i < dataArray.size(); i++) {
                                JSONObject typeItem = dataArray.getJSONObject(i);
                                Integer id = typeItem.getInteger("id");
                                String name = typeItem.getString("name");

                                // 缓存到Redis
                                String cacheKey = "xueke:papertype:name:" + id;
                                CacheKit.cache().set(cacheKey, name, ToolsConst.DAY_SECOND);

                                // 如果是当前需要的类型，添加到本次缓存
                                if (uncachedTypeIds.contains(id)) {
                                    nameCache.put("type:" + id, name);
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("批量加载试卷类型名称异常: " + e.getMessage());
            // 降级处理：为未获取到名称的类型设置默认值
            for (Integer typeId : typeIds) {
                if (!nameCache.containsKey("type:" + typeId)) {
                    nameCache.put("type:" + typeId, "类型" + typeId);
                }
            }
        }
    }

    /**
     * 获取按学段分组的学科列表（QuanPin格式）- 优化版
     *
     * @return 按学段分组的学科列表
     */
    private List<Object> getAllSubjectsGroupedByPeriod() {
        try {
            // 使用缓存提高性能
            String cacheKey = "xueke:subjects:grouped";
            List<Object> cachedResult = CacheKit.cache().get(cacheKey, List.class);

            if (cachedResult != null && !cachedResult.isEmpty()) {
                return cachedResult;
            }

            List<Object> result = new ArrayList<>();

            // 独立实现getSubjects逻辑，不依赖XuekeBuService
            SubjectListDto subjectListDto = this.getSubjectsFromApi();

            // 转换为QuanPin格式
            if (subjectListDto != null) {
                // 处理小学 (xx)
                List<SubjectsDto> xxSubjects = subjectListDto.getXx();
                if (xxSubjects != null && !xxSubjects.isEmpty()) {
                    Map<String, Object> xxGroup = new HashMap<>();
                    xxGroup.put("periodCode", "2");
                    xxGroup.put("name", "小学");
                    xxGroup.put("subjectList", convertSubjectsDtoToQuanPinFormat(xxSubjects));
                    result.add(xxGroup);
                }

                // 处理初中 (cz)
                List<SubjectsDto> czSubjects = subjectListDto.getCz();
                if (czSubjects != null && !czSubjects.isEmpty()) {
                    Map<String, Object> czGroup = new HashMap<>();
                    czGroup.put("periodCode", "3");
                    czGroup.put("name", "初中");
                    czGroup.put("subjectList", convertSubjectsDtoToQuanPinFormat(czSubjects));
                    result.add(czGroup);
                }

                // 处理高中 (gz)
                List<SubjectsDto> gzSubjects = subjectListDto.getGz();
                if (gzSubjects != null && !gzSubjects.isEmpty()) {
                    Map<String, Object> gzGroup = new HashMap<>();
                    gzGroup.put("periodCode", "4");
                    gzGroup.put("name", "高中");
                    gzGroup.put("subjectList", convertSubjectsDtoToQuanPinFormat(gzSubjects));
                    result.add(gzGroup);
                }
            }

            // 缓存结果（缓存6小时）
            if (!result.isEmpty()) {
                CacheKit.cache().set(cacheKey, result, ToolsConst.DAY_SECOND / 4);
            }

            return result;

        } catch (Exception e) {
            System.out.println("按学段分组学科列表异常: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }


    /**
     * 将SubjectsDto列表转换为QuanPin格式
     *
     * @param subjectsDtos SubjectsDto列表
     * @return QuanPin格式的学科列表
     */
    private List<Map<String, Object>> convertSubjectsDtoToQuanPinFormat(List<SubjectsDto> subjectsDtos) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (subjectsDtos != null) {
            for (SubjectsDto subjectsDto : subjectsDtos) {
                Map<String, Object> quanPinSubject = new HashMap<>();

                quanPinSubject.put("subjectCode", subjectsDto.getSubject());
                quanPinSubject.put("name", subjectsDto.getName());
                quanPinSubject.put("course_id", subjectsDto.getCourseId());

                result.add(quanPinSubject);
            }
        }

        return result;
    }

    /**
     * 从API获取学科数据（独立实现，不依赖XuekeBuService）
     *
     * @return SubjectListDto
     */
    private SubjectListDto getSubjectsFromApi() {
        // 组装成subjectListDto
        SubjectListDto subjectListDto = new SubjectListDto();

        // 获取所有课程
        List<CoursesResponseData> courseList = this.coursesAll();
        if (ToolsKit.isNotEmpty(courseList)) {
            for (CoursesResponseData course : courseList) {
                switch (course.getStage_id()) {
                    case 2:
                        // 因为13后的那些科目，不太需要，过滤的科目有，音乐，美术，体育，心理健康，综合实践活动，拓展，书法，劳动，藏语
                        if (course.getSubject_id() > 11) {
                            continue;
                        }
                        SubjectsDto dto = new SubjectsDto();
                        dto.setSubject(String.valueOf(course.getSubject_id()));
                        dto.setName(course.getName().replace("小学", "").replace("道德与法治", "道法").replace("信息科技", "信息").replace("信息技术", "信息").replace("思想政治", "政治"));
                        settingIconSubjectFromCourseAttr(dto);
                        dto.setStageId(String.valueOf(course.getStage_id()));
                        dto.setCourseId(String.valueOf(course.getId()));
                        subjectListDto.getXx().add(dto);
                        break;
                    case 3:
                        // 因为13后的那些科目，不太需要，过滤的科目有，音乐，美术，体育，心理健康，综合实践活动，拓展，书法，劳动，藏语
                        if (course.getSubject_id() > 12) {
                            continue;
                        }
                        if ("初中历史与社会".equals(course.getName())) {
                            continue;
                        }
                        SubjectsDto dtoCz = new SubjectsDto();
                        dtoCz.setSubject(String.valueOf(course.getSubject_id()));
                        dtoCz.setName(course.getName().replace("初中", "").replace("道德与法治", "道法").replace("信息科技", "信息").replace("信息技术", "信息").replace("历史与社会", "历史").replace("生物学", "生物").replace("思想政治", "政治"));
                        settingIconSubjectFromCourseAttr(dtoCz);
                        dtoCz.setStageId(String.valueOf(course.getStage_id()));
                        dtoCz.setCourseId(String.valueOf(course.getId()));
                        subjectListDto.getCz().add(dtoCz);
                        break;
                    case 4:
                        if (course.getSubject_id() > 12) {
                            continue;
                        }
                        SubjectsDto dtoGz = new SubjectsDto();
                        dtoGz.setSubject(String.valueOf(course.getSubject_id()));
                        dtoGz.setName(course.getName().replace("高中", "").replace("道德与法治", "道法").replace("信息科技", "信息").replace("信息技术", "信息").replace("历史与社会", "历史").replace("生物学", "生物").replace("思想政治", "政治"));
                        settingIconSubjectFromCourseAttr(dtoGz);
                        dtoGz.setStageId(String.valueOf(course.getStage_id()));
                        dtoGz.setCourseId(String.valueOf(course.getId()));
                        subjectListDto.getGz().add(dtoGz);
                        break;
                }
            }
        }

        return subjectListDto;
    }

    /**
     * 合并TEXTBOOK_VERSIONS和TEXTBOOKS接口数据，转换为QuanPin格式
     *
     * @param courseId  课程ID
     * @param gradeId   年级ID
     * @param pageIndex 当前页码
     * @param pageSize  每页数据条数
     * @param versionId 教材版本ID
     * @param isUpgrade 是否升学模式
     * @return 版本列表（QuanPin格式）
     */
    private List<VersionDto> getVersionsFromTextbooks(String courseId, String gradeId, String pageIndex, String pageSize, String versionId, String isUpgrade) {
        try {
            // 使用缓存提高性能
            String cacheKey = String.format("xueke:versions:%s:%s:%s:%s:%s:%s",
                    courseId, gradeId, pageIndex, pageSize, versionId, isUpgrade);
            List<VersionDto> cachedResult = CacheKit.cache().get(cacheKey, List.class);

            if (cachedResult != null && !cachedResult.isEmpty()) {
                return cachedResult;
            }

            List<VersionDto> result = new ArrayList<>();

            // 1. 先调用TEXTBOOK_VERSIONS获取版本信息
            Map<String, String> versionNames = getTextbookVersions(courseId);

            // 2. 再调用TEXTBOOKS获取教材列表
            String response = apiService.textbooks(courseId, gradeId, pageIndex, pageSize, versionId);

            if (ToolsKit.isEmpty(response)) {
                return result;
            }

            // 解析textbooks响应
            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject.getInteger("code") == 2000000) {
                JSONObject dataObject = jsonObject.getJSONObject("data");
                if (dataObject != null) {
                    JSONArray itemsArray = dataObject.getJSONArray("items");
                    if (itemsArray != null) {
                        // 按版本分组
                        Map<String, VersionDto> versionMap = new HashMap<>();

                        for (int i = 0; i < itemsArray.size(); i++) {
                            JSONObject textbookItem = itemsArray.getJSONObject(i);

                            // 根据真实API数据结构获取字段
                            String versionCode = String.valueOf(textbookItem.get("version_id"));
                            String textbookId = String.valueOf(textbookItem.get("id"));
                            String textbookName = String.valueOf(textbookItem.get("name"));
                            String volume = String.valueOf(textbookItem.get("volume")); // 册别
                            String gradeIdStr = String.valueOf(textbookItem.get("grade_id"));
                            String courseIdStr = String.valueOf(textbookItem.get("course_id"));
                            String term = String.valueOf(textbookItem.get("term")); // 学期
                            Integer ordinal = textbookItem.getInteger("ordinal"); // 排序值

                            // 创建或获取版本DTO
                            VersionDto versionDto = versionMap.get(versionCode);
                            if (versionDto == null) {
                                versionDto = new VersionDto();
                                versionDto.setVersionCode(versionCode);

                                // 优先使用TEXTBOOK_VERSIONS接口获取的版本名称
                                String versionName = versionNames.get(versionCode);
                                if (ToolsKit.isEmpty(versionName)) {
                                    // 如果没有找到，从教材名称中提取
                                    versionName = extractVersionName(textbookName);
                                }
                                versionDto.setName(versionName);
                                versionDto.setGradeList(new ArrayList<>());
                                versionMap.put(versionCode, versionDto);
                            }

                            // 创建年级DTO，映射所有字段
                            GradeDto gradeDto = new GradeDto();
                            gradeDto.setGradeCode(gradeIdStr);
                            gradeDto.setName(volume); // QuanPin的name对应volume
                            gradeDto.setVolumnName(extractVolumnFromVolume(volume)); // 册别名称
                            gradeDto.setVolumnCode(convertTermToVolumnCode(term)); // 从term转换册别代码

                            // 保留重要的Xueke字段
                            gradeDto.setTextbookId(textbookId);
                            gradeDto.setCourseId(courseIdStr);
                            gradeDto.setVersionId(versionCode);
                            gradeDto.setTerm(term);
                            gradeDto.setOrdinal(ordinal);
                            gradeDto.setTextbookName(textbookName);

                            // findversionobj接口返回QuanPin格式 + 必要的Xueke字段

                            versionDto.getGradeList().add(gradeDto);
                        }

                        result.addAll(versionMap.values());

                        // 如果是升学模式，只保留每个版本的最后一个年级
                        if ("1".equals(isUpgrade)) {
                            result = filterForUpgrade(result);
                        }
                    }
                }
            }

            // 缓存结果（缓存1小时）
            if (!result.isEmpty()) {
                CacheKit.cache().set(cacheKey, result, ToolsConst.DAY_SECOND / 24);
            }

            return result;

        } catch (Exception e) {
            System.out.println("从textbooks获取版本数据异常: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 将Xueke的CATALOG数据转换为QuanPin的BookDto格式
     *
     * @param catalogData Xueke的章节数据
     * @param textbookId  教材ID
     * @return QuanPin格式的BookDto
     */
    private Object convertToQuanPinChapterFormat(List<Object> catalogData, String textbookId) {
        Map<String, Object> bookDto = new HashMap<>();

        try {
            // 创建bookText信息
            Map<String, Object> bookText = new HashMap<>();
            bookText.put("id", textbookId);
            bookText.put("textName", ""); // 教材名称，如果需要可以从其他地方获取
            bookDto.put("bookText", bookText);

            // 转换章节列表
            List<Map<String, Object>> chapters = new ArrayList<>();

            if (catalogData != null && !catalogData.isEmpty()) {
                // 构建章节树结构
                Map<String, Map<String, Object>> chapterMap = new HashMap<>();
                List<Map<String, Object>> rootChapters = new ArrayList<>();

                // 第一遍：创建所有章节对象
                for (Object item : catalogData) {
                    if (item instanceof JSONObject) {
                        JSONObject catalogItem = (JSONObject) item;
                        Map<String, Object> chapter = convertCatalogToChapter(catalogItem);
                        String chapterId = String.valueOf(catalogItem.get("id"));
                        chapterMap.put(chapterId, chapter);
                    }
                }

                // 第二遍：构建父子关系
                for (Object item : catalogData) {
                    if (item instanceof JSONObject) {
                        JSONObject catalogItem = (JSONObject) item;
                        String chapterId = String.valueOf(catalogItem.get("id"));
                        String parentId = String.valueOf(catalogItem.get("parent_id"));

                        Map<String, Object> chapter = chapterMap.get(chapterId);

                        if ("0".equals(parentId)) {
                            // 根节点
                            rootChapters.add(chapter);
                        } else {
                            // 子节点，添加到父节点的children中
                            Map<String, Object> parentChapter = chapterMap.get(parentId);
                            if (parentChapter != null) {
                                @SuppressWarnings("unchecked")
                                List<Map<String, Object>> children = (List<Map<String, Object>>) parentChapter.get("children");
                                if (children == null) {
                                    children = new ArrayList<>();
                                    parentChapter.put("children", children);
                                }
                                children.add(chapter);

                                // 设置父节点hasChild为true
                                parentChapter.put("hasChild", true);
                            }
                        }
                    }
                }

                // 设置章节层级
                setChapterLevels(rootChapters, 1);
                chapters = rootChapters;
            }

            bookDto.put("chapters", chapters);

        } catch (Exception e) {
            System.out.println("转换QuanPin章节格式异常: " + e.getMessage());
            e.printStackTrace();
        }

        return bookDto;
    }

    /**
     * 将单个Xueke章节转换为QuanPin格式
     *
     * @param catalogItem Xueke章节数据
     * @return QuanPin格式的章节
     */
    private Map<String, Object> convertCatalogToChapter(JSONObject catalogItem) {
        Map<String, Object> chapter = new HashMap<>();

        String id = String.valueOf(catalogItem.get("id"));
        String name = String.valueOf(catalogItem.get("name"));
        String parentId = String.valueOf(catalogItem.get("parent_id"));
        String type = String.valueOf(catalogItem.get("type"));
        Integer ordinal = catalogItem.getInteger("ordinal");

        // QuanPin格式字段
        chapter.put("chapterCode", id);
        chapter.put("chapterName", name);
        chapter.put("parentCode", parentId);
        chapter.put("belongBook", String.valueOf(catalogItem.get("textbook_id")));
        chapter.put("catalogueType", type); // REAL/VIRTUAL
        chapter.put("hasChild", false); // 初始设为false，后续根据实际情况设置
        chapter.put("chapterLevel", 1); // 初始层级，后续会重新设置
        chapter.put("children", new ArrayList<>());

        return chapter;
    }

    /**
     * 递归设置章节层级
     *
     * @param chapters 章节列表
     * @param level    当前层级
     */
    private void setChapterLevels(List<Map<String, Object>> chapters, int level) {
        if (chapters != null) {
            for (Map<String, Object> chapter : chapters) {
                chapter.put("chapterLevel", level);

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> children = (List<Map<String, Object>>) chapter.get("children");
                if (children != null && !children.isEmpty()) {
                    setChapterLevels(children, level + 1);
                }
            }
        }
    }

    /**
     * 调用TEXTBOOK_VERSIONS接口获取版本信息
     *
     * @param courseId 课程ID
     * @return 版本ID到版本名称的映射
     */
    private Map<String, String> getTextbookVersions(String courseId) {
        Map<String, String> versionMap = new HashMap<>();

        try {
            String key = String.format(XueKeWangAddressEnums.TEXTBOOK_VERSIONS.getCacheKey(), courseId);
            String response = "";

            if (CacheKit.cache().exists(key)) {
                response = CacheKit.cache().get(key, String.class);
            } else {
                response = apiService.coursesTextbookVersions(courseId);
                if (ToolsKit.isNotEmpty(response)) {
                    CacheKit.cache().set(key, response, ToolsConst.DAY_SECOND);
                }
            }

            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject versionItem = dataArray.getJSONObject(i);
                            String versionId = String.valueOf(versionItem.get("id"));
                            String versionName = String.valueOf(versionItem.get("name"));
                            versionMap.put(versionId, versionName);
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("获取教材版本异常: " + e.getMessage());
            e.printStackTrace();
        }

        return versionMap;
    }

    /**
     * 直接调用CATALOG接口获取章节列表（返回原始数据）
     *
     * @param textbookId 教材ID
     * @return 章节列表（原始格式）
     */
    private List<Object> getTextbookCatalogDirect(String textbookId) {
        List<Object> result = new ArrayList<>();

        try {
            String key = String.format(XueKeWangAddressEnums.CATALOG.getCacheKey(), textbookId);
            String response = "";

            if (CacheKit.cache().exists(key)) {
                response = CacheKit.cache().get(key, String.class);
            } else {
                response = apiService.textbooksCatalog(textbookId);
                if (ToolsKit.isNotEmpty(response)) {
                    CacheKit.cache().set(key, response, ToolsConst.DAY_SECOND);
                }
            }

            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject item = dataArray.getJSONObject(i);
                            // 直接返回原始数据，不做任何转换
                            result.add(item);
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("获取教材章节异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 获取教材章节目录
     *
     * @param textbookId 教材ID
     * @return 章节列表
     */
    private List<Object> getTextbookCatalog(String textbookId) {
        List<Object> result = new ArrayList<>();

        try {
            String key = String.format(XueKeWangAddressEnums.CATALOG.getCacheKey(), textbookId);
            String response = "";

            if (CacheKit.cache().exists(key)) {
                response = CacheKit.cache().get(key, String.class);
            } else {
                response = apiService.textbooksCatalog(textbookId);
                if (ToolsKit.isNotEmpty(response)) {
                    CacheKit.cache().set(key, response, ToolsConst.DAY_SECOND);
                }
            }

            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject item = dataArray.getJSONObject(i);
                            result.add(item);
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("获取教材章节异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 升学模式过滤：只保留每个版本的最后一个年级
     *
     * @param versions 版本列表
     * @return 过滤后的版本列表
     */
    private List<VersionDto> filterForUpgrade(List<VersionDto> versions) {
        List<VersionDto> result = new ArrayList<>();

        for (VersionDto version : versions) {
            if (version.getGradeList() != null && !version.getGradeList().isEmpty()) {
                VersionDto filteredVersion = new VersionDto();
                filteredVersion.setVersionCode(version.getVersionCode());
                filteredVersion.setName(version.getName());

                // 只保留最后一个年级
                List<GradeDto> gradeList = new ArrayList<>();
                GradeDto lastGrade = version.getGradeList().get(version.getGradeList().size() - 1);
                gradeList.add(lastGrade);
                filteredVersion.setGradeList(gradeList);

                result.add(filteredVersion);
            }
        }

        return result;
    }

    /**
     * 从教材名称中提取版本名称
     *
     * @param textbookName 教材名称（如：初中语文统编版（2024）七年级上册）
     * @return 版本名称（如：统编版）
     */
    private String extractVersionName(String textbookName) {
        if (ToolsKit.isEmpty(textbookName)) {
            return "未知版本";
        }

        // 常见版本名称匹配
        if (textbookName.contains("统编版")) return "统编版";
        if (textbookName.contains("人教版")) return "人教版";
        if (textbookName.contains("苏教版")) return "苏教版";
        if (textbookName.contains("北师大版")) return "北师大版";
        if (textbookName.contains("外研版")) return "外研版";
        if (textbookName.contains("译林版")) return "译林版";
        if (textbookName.contains("沪教版")) return "沪教版";
        if (textbookName.contains("鲁教版")) return "鲁教版";
        if (textbookName.contains("冀教版")) return "冀教版";
        if (textbookName.contains("湘教版")) return "湘教版";
        if (textbookName.contains("粤教版")) return "粤教版";

        // 如果没有匹配到，返回默认值
        return "通用版";
    }

    /**
     * 将学期term转换为册别代码
     *
     * @param term 学期（LAST,NEXT,ALL）
     * @return 册别代码
     */
    private String convertTermToVolumnCode(String term) {
        if (ToolsKit.isEmpty(term)) {
            return "0";
        }

        switch (term.toUpperCase()) {
            case "LAST":
                return "1"; // 上册
            case "NEXT":
                return "2"; // 下册
            case "ALL":
                return "0";  // 全册
            default:
                return "0";
        }
    }

    /**
     * 从volume字段中提取册别名称
     *
     * @param volume 册别（如：七年级上册）
     * @return 册别名称（如：上册）
     */
    private String extractVolumnFromVolume(String volume) {
        if (ToolsKit.isEmpty(volume)) {
            return "";
        }

        if (volume.contains("上册")) return "上册";
        if (volume.contains("下册")) return "下册";
        if (volume.contains("全册")) return "全册";

        // 如果没有明确的册别，返回原值
        return volume;
    }

    /**
     * 获取所有课程
     *
     * @return 课程列表
     */
    private List<CoursesResponseData> coursesAll() {
        String key = String.format(XueKeWangAddressEnums.COURSES_ALL.getCacheKey());
        String response = "";
        if (CacheKit.cache().exists(key)) {
            response = CacheKit.cache().get(key, String.class);
        } else {
            response = apiService.coursesAll();
            CacheKit.cache().set(key, response, ToolsConst.MONTH_SECOND);
        }
        CoursesResponseVO repo = JSONObject.parseObject(response, CoursesResponseVO.class);
        checkResponse(repo, null, null);
        return repo.getData();
    }

    /**
     * 设置学科图标和属性
     *
     * @param dto 学科DTO
     */
    private void settingIconSubjectFromCourseAttr(SubjectsDto dto) {
        if (XuekeSubjectTypeEnum.getMap().get(dto.getName()) != null) {
            XuekeSubjectTypeEnum typeEnums = XuekeSubjectTypeEnum.getMap().get(dto.getName());
            dto.setEnglish(typeEnums.getEnglish());
            dto.setSort(typeEnums.getType());
            dto.setIcon(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain() + commonProperties.getH5Env(), typeEnums.getIcon()));
        }
    }

    /**
     * 检查API响应
     *
     * @param repo      响应对象
     * @param errorMsg  错误消息
     * @param errorCode 错误代码
     */
    private void checkResponse(Object repo, String errorMsg, String errorCode) {
        // 简化实现，如果需要可以添加具体的检查逻辑
        if (repo == null) {
            throw new RuntimeException("API响应为空");
        }
    }

// ==================== 私有辅助方法 ====================


    /**
     * 根据版本代码和年级代码查找教材ID
     *
     * @param subjectCode 学科代码
     * @param periodCode  学段代码
     * @param versionCode 版本代码
     * @param gradeCode   年级代码
     * @return 教材ID
     */
    private String findTextbookIdByVersionAndGrade(String subjectCode, String periodCode,
                                                   String versionCode, String gradeCode) {
        try {
            // 获取教材列表
            List<TextBook> textBooks = this.getBookList2(subjectCode, periodCode);

            if (ToolsKit.isEmpty(textBooks)) {
                return "";
            }

            // 解析年级代码，格式可能是 "1,1" 表示一年级上册
            String[] gradeParts = gradeCode.split(",");
            String gradeNumber = gradeParts.length > 0 ? gradeParts[0] : "";
            String volumeNumber = gradeParts.length > 1 ? gradeParts[1] : "0";

            // 查找匹配的教材
            for (TextBook textBook : textBooks) {
                if (textBook.getLeaf() != null && textBook.getLeaf()) {
                    String bookVersion = TextBookNameParseUtil.extractVersionFromName(textBook.getName());
                    String bookVersionCode = TextBookNameParseUtil.generateVersionCode(bookVersion);

                    // 匹配版本代码
                    if (versionCode.equals(bookVersionCode)) {
                        // 进一步匹配年级和册别
                        String[] gradeInfo = TextBookNameParseUtil.extractGradeInfoFromName(textBook.getName());
                        String gradeName = gradeInfo[0];
                        String volumeName = gradeInfo[1];

                        // 简单的年级匹配逻辑
                        if (isGradeMatch(gradeName, gradeNumber) && isVolumeMatch(volumeName, volumeNumber)) {
                            return textBook.getId();
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("查找教材ID异常: " + e.getMessage());
            e.printStackTrace();
        }

        return "";
    }

    /**
     * 判断年级是否匹配
     *
     * @param gradeName   年级名称（如：一年级）
     * @param gradeNumber 年级数字（如：1）
     * @return 是否匹配
     */
    private boolean isGradeMatch(String gradeName, String gradeNumber) {
        if (ToolsKit.isEmpty(gradeName) || ToolsKit.isEmpty(gradeNumber)) {
            return false;
        }

        Map<String, String> gradeMapping = new HashMap<>();
        gradeMapping.put("1", "一年级");
        gradeMapping.put("2", "二年级");
        gradeMapping.put("3", "三年级");
        gradeMapping.put("4", "四年级");
        gradeMapping.put("5", "五年级");
        gradeMapping.put("6", "六年级");
        gradeMapping.put("7", "七年级");
        gradeMapping.put("8", "八年级");
        gradeMapping.put("9", "九年级");

        String expectedGradeName = gradeMapping.get(gradeNumber);
        return gradeName.contains(expectedGradeName);
    }

    /**
     * 判断册别是否匹配
     *
     * @param volumeName   册别名称（如：上册）
     * @param volumeNumber 册别数字（如：1）
     * @return 是否匹配
     */
    private boolean isVolumeMatch(String volumeName, String volumeNumber) {
        if (ToolsKit.isEmpty(volumeName) || ToolsKit.isEmpty(volumeNumber)) {
            return true; // 如果没有指定册别，认为匹配
        }

        if ("0".equals(volumeNumber)) {
            return true; // 0表示全册，都匹配
        }

        Map<String, String> volumeMapping = new HashMap<>();
        volumeMapping.put("1", "上册");
        volumeMapping.put("2", "下册");

        String expectedVolumeName = volumeMapping.get(volumeNumber);
        return volumeName.contains(expectedVolumeName);
    }

// ==================== 从XuekeBuService复制的基础方法 ====================

    /**
     * 获取教材列表
     *
     * @param courseId 课程ID
     * @param stageId  学段ID
     * @return 教材列表
     */
    public List<TextBook> getBookList2(String courseId, String stageId) {
        List<TextBook> result = new ArrayList<>();

        try {
            // 先从缓存获取
            String key = String.format(XueKeWangAddressEnums.TEXTBOOKS_RESULT.getCacheKey(), courseId, stageId);
            String contents = "";
            if (CacheKit.cache().exists(key)) {
                contents = CacheKit.cache().get(key, String.class);
            } else {
                XueKeTextBookCache xueKeTextBookCache = xueKeTextBookCacheDao.getByCourseIdAndStageId(courseId, stageId);
                if (xueKeTextBookCache != null && ToolsKit.isNotEmpty(xueKeTextBookCache.getContents())) {
                    contents = xueKeTextBookCache.getContents();
                    CacheKit.cache().set(key, contents, ToolsConst.DAY_SECOND);
                }
            }

            // 没有值，则返回
            if (ToolsKit.isEmpty(contents)) {
                return result;
            }

            JSONArray jsonArray = JSON.parseArray(contents);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                TextBook textBook = JSON.parseObject(jsonObject.toJSONString(), TextBook.class);
                result.add(textBook);
            }

        } catch (Exception e) {
            System.out.println("获取教材列表异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 获取章节目录
     *
     * @param textbookId 教材ID
     * @return 章节列表
     */
    public List<Object> getCatalogs(String textbookId) {
        List<Object> result = new ArrayList<>();

        try {
            String key = String.format(XueKeWangAddressEnums.CATALOG.getCacheKey(), textbookId);
            String response = "";
            if (CacheKit.cache().exists(key)) {
                response = CacheKit.cache().get(key, String.class);
            } else {
                response = apiService.textbooksCatalog(textbookId);
                if (ToolsKit.isNotEmpty(response)) {
                    CacheKit.cache().set(key, response, ToolsConst.DAY_SECOND);
                }
            }

            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject item = dataArray.getJSONObject(i);
                            result.add(item);
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("获取章节目录异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 获取升学考试类型列表
     *
     * @return 升学考试类型列表
     */
    public List<XuekePaperTypeDto> findPaperTypeListForElevenPlus() {
        List<XuekePaperTypeDto> result = new ArrayList<>();

        try {
            String key = XueKeWangAddressEnums.PAPER_TYPE.getCacheKey();
            String response = "";
            if (CacheKit.cache().exists(key)) {
                response = CacheKit.cache().get(key, String.class);
            } else {
                response = apiService.paperType();
                if (ToolsKit.isNotEmpty(response)) {
                    CacheKit.cache().set(key, response, ToolsConst.DAY_SECOND);
                }
            }

            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject item = dataArray.getJSONObject(i);
                            XuekePaperTypeDto dto = JSON.parseObject(item.toJSONString(), XuekePaperTypeDto.class);
                            // 过滤升学考试类型（这里可以根据实际业务逻辑过滤）
                            if (isElevenPlusType(dto)) {
                                result.add(dto);
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("获取升学考试类型异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 获取阶段考试类型列表
     *
     * @return 阶段考试类型列表
     */
    public List<XuekePaperTypeDto> findPaperTypeListForSectionalExamination() {
        List<XuekePaperTypeDto> result = new ArrayList<>();

        try {
            String key = XueKeWangAddressEnums.PAPER_TYPE.getCacheKey();
            String response = "";
            if (CacheKit.cache().exists(key)) {
                response = CacheKit.cache().get(key, String.class);
            } else {
                response = apiService.paperType();
                if (ToolsKit.isNotEmpty(response)) {
                    CacheKit.cache().set(key, response, ToolsConst.DAY_SECOND);
                }
            }

            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject item = dataArray.getJSONObject(i);
                            XuekePaperTypeDto dto = JSON.parseObject(item.toJSONString(), XuekePaperTypeDto.class);
                            // 过滤阶段考试类型（这里可以根据实际业务逻辑过滤）
                            if (isSectionalExamType(dto)) {
                                result.add(dto);
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("获取阶段考试类型异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }


    /**
     * 判断是否为升学考试类型
     *
     * @param dto 试卷类型DTO
     * @return 是否为升学考试类型
     */
    private boolean isElevenPlusType(XuekePaperTypeDto dto) {
        if (dto == null || ToolsKit.isEmpty(dto.getName())) {
            return false;
        }

        String name = dto.getName().toLowerCase();
        // 根据实际业务逻辑判断，这里是示例
        return name.contains("升学") || name.contains("中考") || name.contains("高考") ||
                name.contains("小升初") || name.contains("初升高") || name.startsWith("学业")
                || name.startsWith("自主") || name.startsWith("中职") || name.startsWith("强基") || name.contains("模");
    }

    /**
     * 判断是否为阶段考试类型
     *
     * @param dto 试卷类型DTO
     * @return 是否为阶段考试类型
     */
    private boolean isSectionalExamType(XuekePaperTypeDto dto) {
        if (dto == null || ToolsKit.isEmpty(dto.getName())) {
            return false;
        }

        String name = dto.getName().toLowerCase();
        // 根据实际业务逻辑判断，这里是示例
        return name.contains("阶段") || name.contains("期中") || name.contains("期末") ||
                name.contains("月考") || name.contains("周测") || name.contains("专题") || name.contains("开学");
    }


    /**
     * 获取作业类型列表
     *
     * @return 作业类型列表
     */
    public List<XuekePaperTypeDto> findPaperTypeListForHomework() {
        List<XuekePaperTypeDto> result = new ArrayList<>();

        try {
            String key = XueKeWangAddressEnums.PAPER_TYPE.getCacheKey();
            String response = "";
            if (CacheKit.cache().exists(key)) {
                response = CacheKit.cache().get(key, String.class);
            } else {
                response = apiService.paperType();
                if (ToolsKit.isNotEmpty(response)) {
                    CacheKit.cache().set(key, response, ToolsConst.DAY_SECOND);
                }
            }

            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject item = dataArray.getJSONObject(i);
                            XuekePaperTypeDto dto = JSON.parseObject(item.toJSONString(), XuekePaperTypeDto.class);
                            // 过滤作业类型（这里可以根据实际业务逻辑过滤）
                            if (isHomeworkType(dto)) {
                                result.add(dto);
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("获取作业类型异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 判断是否为同步练习
     *
     * @param dto 试卷类型DTO
     * @return 是否为同步练习
     */
    private boolean isHomeworkType(XuekePaperTypeDto dto) {
        if (dto == null || ToolsKit.isEmpty(dto.getName())) {
            return false;
        }

        String name = dto.getName().toLowerCase();
        // 根据实际业务逻辑判断，这里是示例
        return name.contains("作业") || name.contains("随堂") || name.contains("课前") ||
                name.contains("课后") || name.contains("习题") || name.contains("单元") || name.contains("课堂");
    }

    /**
     * 根据学段ID获取学段名称
     *
     * @param stageId 学段ID
     * @return 学段名称
     */
    private String getStageNameById(Integer stageId) {
        if (stageId == null) return "";

        switch (stageId) {
            case 1:
                return "学前";
            case 2:
                return "小学";
            case 3:
                return "初中";
            case 4:
                return "高中";
            case 5:
                return "大学";
            case 6:
                return "中职";
            default:
                return "";
        }
    }

    /**
     * 根据课程ID获取课程名称（带缓存优化）
     *
     * @param courseId 课程ID
     * @return 课程名称
     */
    private String getCourseNameById(Integer courseId) {
        if (courseId == null) return "";

        try {
            // 使用缓存提高性能
            String cacheKey = "xueke:course:name:" + courseId;
            String courseName = CacheKit.cache().get(cacheKey, String.class);

            if (ToolsKit.isNotEmpty(courseName)) {
                return courseName;
            }

            // 缓存未命中，调用API获取课程信息
            String response = apiService.coursesAll();
            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject courseItem = dataArray.getJSONObject(i);
                            Integer id = courseItem.getInteger("id");
                            String name = courseItem.getString("name");

                            // 缓存所有课程名称，减少后续查询
                            String key = "xueke:course:name:" + id;
                            CacheKit.cache().set(key, name, ToolsConst.DAY_SECOND); // 缓存24小时

                            if (courseId.equals(id)) {
                                courseName = name;
                            }
                        }
                    }
                }
            }

            return ToolsKit.isNotEmpty(courseName) ? courseName : "未知课程";

        } catch (Exception e) {
            System.out.println("获取课程名称异常: " + e.getMessage());
            return "课程" + courseId;
        }
    }

    /**
     * 根据年级ID获取年级名称（带缓存优化）
     *
     * @param gradeId 年级ID
     * @return 年级名称
     */
    private String getGradeNameById(Integer gradeId) {
        if (gradeId == null) return "";

        try {
            // 使用缓存提高性能
            String cacheKey = "xueke:grade:name:" + gradeId;
            String gradeName = CacheKit.cache().get(cacheKey, String.class);

            if (ToolsKit.isNotEmpty(gradeName)) {
                return gradeName;
            }

            // 缓存未命中，调用API获取年级信息
            // 尝试获取不同学制的年级信息
            String[] divisions = {"G63", "G54"}; // 六三制、五四制
            Integer[] stageIds = {1, 2, 3, 4, 5, 6}; // 各个学段

            for (String division : divisions) {
                for (Integer stageId : stageIds) {
                    try {
                        String response = apiService.grades(division, stageId);
                        if (ToolsKit.isNotEmpty(response)) {
                            JSONObject jsonObject = JSON.parseObject(response);
                            if (jsonObject.getInteger("code") == 2000000) {
                                JSONArray dataArray = jsonObject.getJSONArray("data");
                                if (dataArray != null) {
                                    for (int i = 0; i < dataArray.size(); i++) {
                                        JSONObject gradeItem = dataArray.getJSONObject(i);
                                        Integer id = gradeItem.getInteger("id");
                                        String name = gradeItem.getString("name");

                                        // 缓存所有年级名称
                                        String key = "xueke:grade:name:" + id;
                                        CacheKit.cache().set(key, name, ToolsConst.DAY_SECOND); // 缓存24小时

                                        if (gradeId.equals(id)) {
                                            gradeName = name;
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 忽略单个API调用失败，继续尝试其他组合
                    }

                    // 如果已经找到目标年级名称，提前退出
                    if (ToolsKit.isNotEmpty(gradeName)) {
                        break;
                    }
                }
                if (ToolsKit.isNotEmpty(gradeName)) {
                    break;
                }
            }

            return ToolsKit.isNotEmpty(gradeName) ? gradeName : "未知年级";

        } catch (Exception e) {
            System.out.println("获取年级名称异常: " + e.getMessage());
            // 降级处理：使用简化的年级名称映射
            if (gradeId <= 6) {
                return gradeId + "年级";
            } else if (gradeId <= 9) {
                return "初" + (gradeId - 6) + "年级";
            } else if (gradeId <= 12) {
                return "高" + (gradeId - 9) + "年级";
            }
            return "年级" + gradeId;
        }
    }

    /**
     * 根据类型ID获取类型名称（带缓存优化）
     *
     * @param typeId 类型ID
     * @return 类型名称
     */
    private String getTypeNameById(Integer typeId) {
        if (typeId == null) return "";

        try {
            // 使用缓存提高性能
            String cacheKey = "xueke:papertype:name:" + typeId;
            String typeName = CacheKit.cache().get(cacheKey, String.class);

            if (ToolsKit.isNotEmpty(typeName)) {
                return typeName;
            }

            // 缓存未命中，调用API获取试卷类型信息
            String response = apiService.paperTypes();
            if (ToolsKit.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject typeItem = dataArray.getJSONObject(i);
                            Integer id = typeItem.getInteger("id");
                            String name = typeItem.getString("name");

                            // 缓存所有试卷类型名称
                            String key = "xueke:papertype:name:" + id;
                            CacheKit.cache().set(key, name, ToolsConst.DAY_SECOND); // 缓存24小时

                            if (typeId.equals(id)) {
                                typeName = name;
                            }
                        }
                    }
                }
            }

            return ToolsKit.isNotEmpty(typeName) ? typeName : "未知类型";

        } catch (Exception e) {
            System.out.println("获取试卷类型名称异常: " + e.getMessage());
            return "类型" + typeId;
        }
    }

    /**
     * 将日期字符串转换为时间戳
     *
     * @param dateString 日期字符串
     * @return 时间戳
     */
    private Integer convertDateToTimestamp(String dateString) {
        if (ToolsKit.isEmpty(dateString)) {
            return null;
        }

        try {
            // 假设日期格式为 "2024-01-01" 或 "2024-01-01 12:00:00"
            java.text.SimpleDateFormat sdf;
            if (dateString.length() > 10) {
                sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            } else {
                sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
            }
            java.util.Date date = sdf.parse(dateString);
            return (int) (date.getTime() / 1000);
        } catch (Exception e) {
            System.out.println("日期转换异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 预加载常用数据到缓存（可在应用启动时调用）
     * 提高首次查询性能
     */
    public void preloadCommonData() {
        try {
            System.out.println("开始预加载Xueke常用数据...");

            // 预加载课程数据
            String coursesResponse = apiService.coursesAll();
            if (ToolsKit.isNotEmpty(coursesResponse)) {
                JSONObject jsonObject = JSON.parseObject(coursesResponse);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject courseItem = dataArray.getJSONObject(i);
                            Integer id = courseItem.getInteger("id");
                            String name = courseItem.getString("name");
                            String key = "xueke:course:name:" + id;
                            CacheKit.cache().set(key, name, ToolsConst.DAY_SECOND);
                        }
                        System.out.println("预加载课程数据完成，共" + dataArray.size() + "条");
                    }
                }
            }

            // 预加载试卷类型数据
            String paperTypesResponse = apiService.paperTypes();
            if (ToolsKit.isNotEmpty(paperTypesResponse)) {
                JSONObject jsonObject = JSON.parseObject(paperTypesResponse);
                if (jsonObject.getInteger("code") == 2000000) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    if (dataArray != null) {
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject typeItem = dataArray.getJSONObject(i);
                            Integer id = typeItem.getInteger("id");
                            String name = typeItem.getString("name");
                            String key = "xueke:papertype:name:" + id;
                            CacheKit.cache().set(key, name, ToolsConst.DAY_SECOND);
                        }
                        System.out.println("预加载试卷类型数据完成，共" + dataArray.size() + "条");
                    }
                }
            }

            System.out.println("Xueke常用数据预加载完成！");

        } catch (Exception e) {
            System.out.println("预加载数据异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 清理所有缓存（用于缓存刷新）
     */
    public void clearAllCache() {
        try {
            System.out.println("开始清理Xueke缓存...");

            // 清理学科缓存
            CacheKit.cache().del("xueke:subjects:all");
            CacheKit.cache().del("xueke:subjects:grouped");

            // 清理试卷类型缓存（需要遍历所有可能的学科）
            String[] commonSubjects = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"};
            for (String subjectCode : commonSubjects) {
                CacheKit.cache().del("xueke:papertype:homework:" + subjectCode);
                CacheKit.cache().del("xueke:papertype:sectional:" + subjectCode);
                CacheKit.cache().del("xueke:papertype:eleven:" + subjectCode);
            }

            System.out.println("Xueke缓存清理完成！");

        } catch (Exception e) {
            System.out.println("清理缓存异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 检查主要缓存的命中情况
            stats.put("subjects_all_cached", CacheKit.cache().exists("xueke:subjects:all"));
            stats.put("subjects_grouped_cached", CacheKit.cache().exists("xueke:subjects:grouped"));

            // 统计试卷类型缓存
            int paperTypeCacheCount = 0;
            String[] commonSubjects = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"};
            for (String subjectCode : commonSubjects) {
                if (CacheKit.cache().exists("xueke:papertype:homework:" + subjectCode)) paperTypeCacheCount++;
                if (CacheKit.cache().exists("xueke:papertype:sectional:" + subjectCode)) paperTypeCacheCount++;
                if (CacheKit.cache().exists("xueke:papertype:eleven:" + subjectCode)) paperTypeCacheCount++;
            }
            stats.put("paper_type_cache_count", paperTypeCacheCount);

            // 统计名称缓存
            int nameCacheCount = 0;
            for (int i = 1; i <= 20; i++) {
                if (CacheKit.cache().exists("xueke:course:name:" + i)) nameCacheCount++;
                if (CacheKit.cache().exists("xueke:grade:name:" + i)) nameCacheCount++;
                if (CacheKit.cache().exists("xueke:papertype:name:" + i)) nameCacheCount++;
            }
            stats.put("name_cache_count", nameCacheCount);

        } catch (Exception e) {
            stats.put("error", e.getMessage());
        }

        return stats;
    }

    public JSONObject getData(String result) {
        JSONObject jsonObject = JSON.parseObject(result);
        if (jsonObject.get("code").toString().equals("2000000")) {
            return jsonObject.getJSONObject("data");
        } else {
            return null;
        }
    }

    //根据chapterCode筛选章节数据
    public Object getChapterList(Object catalogData, String chapterCode) {
        Map<String, Object> bookDto = new HashMap<>();
        Map<String, Object> catalogMap = (Map<String, Object>) catalogData;
        // 创建bookText信息
        bookDto.put("bookText", catalogMap.get("bookText"));
        List<Map<String, Object>> catalogList = (List<Map<String, Object>>) catalogMap.get("chapters");
        if ("0".equals(chapterCode)) {
            bookDto.put("chapters", catalogList.stream()
                    .filter(catalog -> "0".equals(catalog.get("parentCode")))
                    .peek(item -> item.put("children", new ArrayList<>())) // 使用peek处理副作用
                    .collect(Collectors.toList()));
        } else {
            bookDto.put("chapters", catalogList.stream().filter(catalog -> chapterCode.equals(catalog.get("chapterCode"))).collect(Collectors.toList()));
        }
        return bookDto;
    }

    // 循环验证第三方接口
    public Object validateItems(Object catalogData, String courseId, String versionCode, String gradeCode, String type) {
        // 基础间隔0秒
        // 随机延迟0-1秒
        Map<String, Object> bookDto = new HashMap<>();
        try {
            List<Map<String, Object>> results = new ArrayList<>();

            // 验证章节
            Map<String, Object> catalogMap = (Map<String, Object>) catalogData;
            // 创建bookText信息
            bookDto.put("bookText", catalogMap.get("bookText"));
            Object catalogList = catalogMap.get("chapters");
            for (Map<String, Object> catalog : (List<Map<String, Object>>) catalogList) {
                List<Map<String, Object>> childList = (List<Map<String, Object>>) catalog.get("children");
                List<Map<String, Object>> modifiedChildList = new ArrayList<>();
                // 检查子章节是否有试卷
                for (Map<String, Object> child : childList) {
                    String chapterCode = child.get("chapterCode").toString();
                    if (checkChapterHasPaperFast(chapterCode, courseId, versionCode, gradeCode, type)) {
                        modifiedChildList.add(child);
                    }
                }
                // 子章节有试卷，则将其添加到结果列表
                if (!modifiedChildList.isEmpty()) {
                    Map<String, Object> modifiedCatalog = new HashMap<>(catalog);
                    modifiedCatalog.put("children", modifiedChildList);
                    modifiedCatalog.put("hasChild", true);
                    results.add(modifiedCatalog);
                } else {
                    String parentChapterCode = catalog.get("chapterCode").toString();
                    if (checkChapterHasPaperFast(parentChapterCode, courseId, versionCode, gradeCode, type)) {
                        // 父章节有试卷，创建新章节对象并清空子章节
                        Map<String, Object> modifiedCatalog = new HashMap<>(catalog);
                        modifiedCatalog.put("children", new ArrayList<>());
                        modifiedCatalog.put("hasChild", false);
                        results.add(modifiedCatalog);
                    }
                    // 父章节也没有试卷则不添加
                }
            }
            bookDto.put("chapters", results);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return bookDto;
    }

    // 优化的检查方法，添加超时和快速失败机制
    private boolean checkChapterHasPaperFast(String chapterCode, String courseId, String versionCode, String gradeCode, String type) {
        try {
            // 使用较小的超时时间，避免长时间等待
            return thirdPartyCallUtil.callWithRandomInterval(
                    "xueke/exercise/findchapterlist",
                    200,      // 基础间隔0秒
                    100,    // 减少随机延迟到0-0.5秒
                    () -> validateWithPaper(chapterCode, courseId, versionCode, gradeCode, type)
            );
        } catch (Exception e) {
            System.err.println("检查章节是否有试卷异常: " + e.getMessage());
            // 发生异常时快速返回false
            return false;
        }
    }

    public Boolean validateWithPaper(String chapterCode, String courseId, String versionCode, String gradeCode, String type) {
        try {
            PaperListDto paperListDto = findPapersListQuanPinFormat(null, courseId, null, versionCode, gradeCode,
                    chapterCode, type, null, null, 1, 1);
            return paperListDto != null && !paperListDto.getList().isEmpty() && paperListDto.getTotalCount() > 0;
        } catch (Exception e) {
            return false;
        }
    }

}
