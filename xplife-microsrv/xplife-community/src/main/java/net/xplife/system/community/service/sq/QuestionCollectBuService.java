package net.xplife.system.community.service.sq;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import net.xplife.system.tools.common.core.ToolsConst;
import net.xplife.system.tools.common.enums.ExceptionEnums;
import net.xplife.system.tools.common.exception.ServiceException;
import net.xplife.system.tools.util.core.ToolsKit;
import net.xplife.system.web.common.JsonKit;
import net.xplife.system.web.config.CommonProperties;
import net.xplife.system.community.dao.sq.QuestionCollectDao;
import net.xplife.system.community.dto.label.LabelDto;
import net.xplife.system.community.dto.sq.AddErrorBookNewDto;
import net.xplife.system.community.dto.sq.QuestionCollectDto;
import net.xplife.system.community.dto.sq.SearchInfoDto;
import net.xplife.system.community.dto.sq.SearchLabelDto;
import net.xplife.system.community.entity.sq.CollectLabel;
import net.xplife.system.community.entity.sq.QuestionCollect;
import net.xplife.system.community.enums.sq.ErrorBookSearchDateEnums;
import net.xplife.system.community.enums.sq.ErrorBookSearchDegreeEnums;
import net.xplife.system.community.enums.sq.ErrorBookSearchDifficEnums;
import net.xplife.system.community.enums.sq.ErrorBookSearchTypeEnums;
import net.xplife.system.community.vo.sq.LabelVo;
@Service
public class QuestionCollectBuService {
    @Autowired
    private QuestionCollectDao    questionCollectDao;
    @Autowired
    private CommonProperties      commonProperties;
    @Autowired
    private CollectLabelBuService collectLabelBuService;

    /**
     * 根据标签ID获取数量
     * 
     * @param userId
     * @param labelId
     * @return
     */
    public long count(String userId, String labelId) {
        return questionCollectDao.count(userId, labelId);
    }

    /**
     * 根据标签ID删除
     * 
     * @param userId
     *            用户ID
     * @param labelId
     *            标签ID
     * @return
     */
    public void delCollect(String userId, String labelId) {
        questionCollectDao.delCollect(userId, labelId);
    }

    /**
     * 获取错题收藏列表
     * 
     * @param userId
     *            用户ID
     * @param labelId
     *            类型
     * @param pageNo
     *            当前页码
     * @param pageSize
     *            每页大小
     * @param lastId
     *            最后一条数据ID
     * @return
     * @throws ServiceException
     */
    public List<QuestionCollectDto> getQuestionList(String userId, String labelId, String date, String errorType, String errorDiffic, String errorReason,
            String errorDegree, String errorSource, String customLabel, int pageNo, int pageSize, String lastId) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(labelId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("目录标签不能为空");
        }
        List<QuestionCollectDto> dtoList = new ArrayList<QuestionCollectDto>();
        try {
            if (pageNo > 0) {
                pageNo--;
            }
            List<QuestionCollect> qcList = questionCollectDao.findQuestionCollectList(userId, labelId, errorType, errorDiffic, errorReason, errorDegree,
                    errorSource, customLabel, pageNo, pageSize, lastId, this.getDate(date), this.getDirection(date));
            CollectLabel collectLabel = collectLabelBuService.getCollectLabel(userId);
            if (ToolsKit.isNotEmpty(qcList)) {
                for (QuestionCollect questionCollect : qcList) {
                    List<LabelDto> labels = new ArrayList<LabelDto>();
                    QuestionCollectDto dto = new QuestionCollectDto();
                    dto.setId(questionCollect.getId());
                    dto.setUrl(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), questionCollect.getPic()));
                    collectLabel.getLabelList().forEach(item -> {
                        if (questionCollect.getLabelId().equals(item.getId())) {
                            dto.setLabelName(item.getName());
                            labels.add(new LabelDto(item.getId(), item.getName(), Integer.parseInt(ErrorBookSearchTypeEnums.SUBJECT.getType())));
                        }
                    });
                    if (ToolsKit.isNotEmpty(collectLabel.getErrorType()) && ToolsKit.isNotEmpty(questionCollect.getErrorType())) {
                        collectLabel.getErrorType().forEach(item -> {
                            if (questionCollect.getErrorType().equals(item.getId())) {
                                labels.add(new LabelDto(item.getId(), item.getName(), Integer.parseInt(ErrorBookSearchTypeEnums.TYPE.getType())));
                            }
                        });
                    }
                    if (ToolsKit.isNotEmpty(collectLabel.getErrorReason()) && ToolsKit.isNotEmpty(questionCollect.getErrorReason())) {
                        collectLabel.getErrorReason().forEach(item -> {
                            if (questionCollect.getErrorReason().equals(item.getId())) {
                                labels.add(new LabelDto(item.getId(), item.getName(), Integer.parseInt(ErrorBookSearchTypeEnums.REASON.getType())));
                            }
                        });
                    }
                    if (ToolsKit.isNotEmpty(collectLabel.getErrorSource()) && ToolsKit.isNotEmpty(questionCollect.getErrorSource())) {
                        collectLabel.getErrorSource().forEach(item -> {
                            if (questionCollect.getErrorSource().equals(item.getId())) {
                                labels.add(new LabelDto(item.getId(), item.getName(), Integer.parseInt(ErrorBookSearchTypeEnums.SOURCE.getType())));
                            }
                        });
                    }
                    if (ToolsKit.isNotEmpty(collectLabel.getCustomLabel()) && ToolsKit.isNotEmpty(questionCollect.getCustomLabel())) {
                        collectLabel.getCustomLabel().forEach(item -> {
                            if (questionCollect.getCustomLabel().equals(item.getId())) {
                                labels.add(new LabelDto(item.getId(), item.getName(), Integer.parseInt(ErrorBookSearchTypeEnums.CUSTOM.getType())));
                            }
                        });
                    }
                    for (ErrorBookSearchDifficEnums diffic : ErrorBookSearchDifficEnums.values()) {
                        if (diffic.getType().equals(questionCollect.getErrorDiffic())) {
                            labels.add(new LabelDto(diffic.getType(), diffic.getValue(), Integer.parseInt(ErrorBookSearchTypeEnums.DIFFIC.getType())));
                            break;
                        }
                    }
                    for (ErrorBookSearchDegreeEnums degree : ErrorBookSearchDegreeEnums.values()) {
                        if (degree.getType().equals(questionCollect.getErrorDegree())) {
                            labels.add(new LabelDto(degree.getType(), degree.getValue(), Integer.parseInt(ErrorBookSearchTypeEnums.DEGREE.getType())));
                            break;
                        }
                    }
                    dto.setLabels(labels);
                    dto.setCreateTime(questionCollect.getCreatetime());
                    if (ToolsKit.isNotEmpty(questionCollect.getAnalysisList())){
                        dto.setAnalysis(new ArrayList<>());
                        for (String str:questionCollect.getAnalysisList()) {
                            dto.getAnalysis().add(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), str));
                        }
                    }

                    String currDate = ToolsKit.Date.format(new Date(), "yyyy-MM-dd");
                    if (currDate.equals(ToolsKit.Date.format(questionCollect.getCreatetime(), "yyyy-MM-dd"))) {
                        dto.setFmtTime("今天");
                    } else {
                        dto.setFmtTime(ToolsKit.Date.format(questionCollect.getCreatetime(), "yyyy-MM-dd"));
                    }
                    dtoList.add(dto);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }

    public Date[] getDate(String date) {
        Date start = null;
        Date end = new Date();
        if (ErrorBookSearchDateEnums.THREE_DAY.getType().equals(date)) {
            start = ToolsKit.Date.offsetDay(end, -3);
        } else if (ErrorBookSearchDateEnums.ONE_WEEK.getType().equals(date)) {
            start = ToolsKit.Date.offsetDay(end, -7);
        } else if (ErrorBookSearchDateEnums.ONE_MONTH.getType().equals(date)) {
            start = ToolsKit.Date.offsetDay(end, -30);
        } else {
            return null;
        }
        return new Date[] { start, end};
    }

    /**
     * 获取排序
     * 
     * @param date
     * @return
     */
    public Direction getDirection(String date) {
        if (ErrorBookSearchDateEnums.ASC.getType().equals(date)) {
            return Sort.Direction.ASC;
        }
        return Sort.Direction.DESC;
    }

    /**
     * 删除错题收藏
     * 
     * @param ids
     *            记录ID
     * @throws ServiceException
     */
    public void delQuestionCollect(String userId, String ids) throws ServiceException {
        if (ToolsKit.isEmpty(ids)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录ID不能为空");
        }
        List<String> idList = JsonKit.jsonParseArray(ids, String.class);
        idList.forEach(item -> {
            QuestionCollect questionCollect = questionCollectDao.getById(item, ToolsConst.DATA_SUCCESS_STATUS);
            if (ToolsKit.isNotEmpty(questionCollect)) {
                questionCollect.setStatus(ToolsConst.DATA_DELETE_STATUS);
                questionCollectDao.saveEntity(questionCollect);
                collectLabelBuService.updateCount(userId, questionCollect.getLabelId(), false);
            }
        });
    }

    /**
     * 添加错题收藏
     * 
     * @param userId
     *            用户ID
     * @param labelId
     *            标签ID
     * @param pic
     *            图片地址
     * @throws ServiceException
     */
    public void addOrUpdate(String id, String userId, String labelId, String pic, String analysis) throws ServiceException {
        if (ToolsKit.isEmpty(pic)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("图片地址不能为空");
        }
        List<String> picList = JsonKit.jsonParseArray(pic, String.class);
        List<String> analysisList = new ArrayList<>();
        if (ToolsKit.isNotEmpty(analysis)){
            List<String> analysisListTemp = JsonKit.jsonParseArray(analysis, String.class);
            for (String str : analysisListTemp) {
                analysisList.add(ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), str));
            }
        }
        if (ToolsKit.isNotEmpty(analysisList) && analysisList.size()>3){
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("题目解析不得超过3个");
        }

        List<String> finalAnalysisList = analysisList;
        picList.forEach(item -> {
            item = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), item);
            QuestionCollect questionCollect = questionCollectDao.getById(id, ToolsConst.DATA_SUCCESS_STATUS);
            boolean isAdd = false;
            if (ToolsKit.isEmpty(questionCollect)) {
                if (ToolsKit.isEmpty(labelId)) {
                    throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("科目ID不能为空");
                }
                questionCollect = new QuestionCollect();
                ToolsKit.setIdEntityData(questionCollect, userId);
                questionCollect.setUserId(userId);
                questionCollect.setLabelId(labelId);
                isAdd = true;
            }
            questionCollect.setPic(item);
            questionCollect.setAnalysisList(finalAnalysisList);
            questionCollectDao.saveEntity(questionCollect);
            if (isAdd) {
                collectLabelBuService.updateCount(userId, labelId, isAdd);
            }
        });
    }

    /**
     * 获取错题本筛选维度
     * 
     * @param userId
     * @return
     * @throws ServiceException
     */
    public SearchInfoDto getSearchInfo(String userId) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        CollectLabel collectLabel = collectLabelBuService.getCollectLabel(userId);
        List<SearchLabelDto> errorSubject = new ArrayList<SearchLabelDto>();
        collectLabelBuService.getCollectLabelList(userId).forEach(item -> {
            errorSubject.add(new SearchLabelDto(item.getId(), item.getName()));
        });
        SearchInfoDto searchInfoDto = new SearchInfoDto();
        List<SearchLabelDto> errorDate = new ArrayList<SearchLabelDto>();
        for (ErrorBookSearchDateEnums errorBookSearchDateEnums : ErrorBookSearchDateEnums.values()) {
            errorDate.add(new SearchLabelDto(errorBookSearchDateEnums.getType(), errorBookSearchDateEnums.getValue()));
        }
        searchInfoDto.setErrorDate(errorDate);
        searchInfoDto.setErrorSubject(errorSubject);
        searchInfoDto.setErrorType(this.getSearchLabel(collectLabel.getErrorType()));
        List<SearchLabelDto> errorDiffic = new ArrayList<SearchLabelDto>();
        for (ErrorBookSearchDifficEnums errorBookSearchDifficEnums : ErrorBookSearchDifficEnums.values()) {
            errorDiffic.add(new SearchLabelDto(errorBookSearchDifficEnums.getType(), errorBookSearchDifficEnums.getValue()));
        }
        searchInfoDto.setErrorDiffic(errorDiffic);
        searchInfoDto.setErrorReason(this.getSearchLabel(collectLabel.getErrorReason()));
        List<SearchLabelDto> errorDegree = new ArrayList<SearchLabelDto>();
        for (ErrorBookSearchDegreeEnums errorBookSearchDegreeEnums : ErrorBookSearchDegreeEnums.values()) {
            errorDegree.add(new SearchLabelDto(errorBookSearchDegreeEnums.getType(), errorBookSearchDegreeEnums.getValue()));
        }
        searchInfoDto.setErrorDegree(errorDegree);
        searchInfoDto.setErrorSource(this.getSearchLabel(collectLabel.getErrorSource()));
        searchInfoDto.setCustomLabel(this.getSearchLabel(collectLabel.getCustomLabel()));
        return searchInfoDto;
    }

    public List<SearchLabelDto> getSearchLabel(List<LabelVo> labelList) {
        List<SearchLabelDto> dtoList = new ArrayList<SearchLabelDto>();
        if (ToolsKit.isNotEmpty(labelList)) {
            labelList.forEach(item -> {
                SearchLabelDto dto = new SearchLabelDto();
                ToolsKit.Bean.copyProperties(item, dto);
                dtoList.add(dto);
            });
        }
        return dtoList;
    }

    /**
     * 判断是否是中文
     * 
     * @param value
     * @return
     */
    public boolean isChinese(String value) {
        if (value.matches("[\u4E00-\u9FA5]+")) {
            return true;
        }
        return false;
    }

    /**
     * 获取字段串长度-中文算两个 其他算一个
     * 
     * @param value
     * @return
     */
    public int getLengthByString(String value) {
        int length = 0;
        for (int i = 0; i < value.length(); i++) {
            String tmp = String.valueOf(value.charAt(i));
            if (isChinese(tmp)) {
                length += 2;
            } else {
                length += 1;
            }
        }
        return length;
    }

    /**
     * 添加错题本筛选维度
     * 
     * @param userId
     *            用户ID
     * @param name
     *            名称
     * @param type
     *            类型
     * @return
     */
    public Map<String, Object> addErrorBookSearch(String userId, String name, String type) throws ServiceException {
        if (ToolsKit.isEmpty(name)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("名称不能为空");
        }
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }
        if (getLengthByString(name) > 10) {
            throw new ServiceException("名称超出长度");
        }
        CollectLabel collectLabel = collectLabelBuService.getCollectLabel(userId);
        String id = StringUtils.EMPTY;
        for (ErrorBookSearchTypeEnums errorBookSearchTypeEnums : ErrorBookSearchTypeEnums.values()) {
            if (errorBookSearchTypeEnums.getType().equals(type)) {
                id = new ObjectId().toString();
                LabelVo labelVo = new LabelVo();
                labelVo.setId(id);
                labelVo.setName(name);
                if (type.equals(ErrorBookSearchTypeEnums.SUBJECT.getType())) {
                    this.addLabelVo(name, collectLabel.getLabelList(), id, labelVo);
                } else if (type.equals(ErrorBookSearchTypeEnums.TYPE.getType())) {
                    this.addLabelVo(name, collectLabel.getErrorType(), id, labelVo);
                } else if (type.equals(ErrorBookSearchTypeEnums.REASON.getType())) {
                    this.addLabelVo(name, collectLabel.getErrorReason(), id, labelVo);
                } else if (type.equals(ErrorBookSearchTypeEnums.SOURCE.getType())) {
                    this.addLabelVo(name, collectLabel.getErrorSource(), id, labelVo);
                } else if (type.equals(ErrorBookSearchTypeEnums.CUSTOM.getType())) {
                    this.addLabelVo(name, collectLabel.getCustomLabel(), id, labelVo);
                }
                collectLabelBuService.save(collectLabel);
                break;
            }
        }
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("id", id);
        return result;
    }

    private void addLabelVo(String name, List<LabelVo> voList, String id, LabelVo labelVo) throws ServiceException {
        LabelVo isExist = voList.stream().filter(item -> item.getName().equals(name)).findAny().orElse(null);// 防止重复
        if (ToolsKit.isEmpty(isExist)) {
            voList.add(labelVo);
        } else {
            throw new ServiceException("标签已存在");
        }
    }

    /**
     * 删除错题本筛选维度
     * 
     * @param userId
     *            用户ID
     * @param id
     *            记录ID
     * @param type
     *            类型
     * @throws ServiceException
     */
    public void delErrorBookSearch(String userId, String id, String type) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录ID不能为空");
        }
        if (ToolsKit.isEmpty(type)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("类型不能为空");
        }
        List<String> idList = JsonKit.jsonParseArray(id, String.class);
        CollectLabel collectLabel = collectLabelBuService.getCollectLabel(userId);
        idList.forEach(recordId -> {
            if (type.equals(ErrorBookSearchTypeEnums.SUBJECT.getType())) {
                collectLabel.getLabelList().removeIf(item -> item.getId().equals(recordId));
            } else if (type.equals(ErrorBookSearchTypeEnums.TYPE.getType())) {
                collectLabel.getErrorType().removeIf(item -> item.getId().equals(recordId));
            } else if (type.equals(ErrorBookSearchTypeEnums.REASON.getType())) {
                collectLabel.getErrorReason().removeIf(item -> item.getId().equals(recordId));
            } else if (type.equals(ErrorBookSearchTypeEnums.SOURCE.getType())) {
                collectLabel.getErrorSource().removeIf(item -> item.getId().equals(recordId));
            } else if (type.equals(ErrorBookSearchTypeEnums.CUSTOM.getType())) {
                collectLabel.getCustomLabel().removeIf(item -> item.getId().equals(recordId));
            }
        });
        collectLabelBuService.save(collectLabel);
    }

    /**
     * 添加错题本
     * 
     * @param addErrorBookNewDto
     * @since 1.9.5
     * @return
     */
    public Map<String, Object> addErrorBookNew(AddErrorBookNewDto addErrorBookNewDto, String analysis) {
        if (ToolsKit.isEmpty(addErrorBookNewDto.getImgUrl())) {
            throw new ServiceException("图片不能为空");
        }

        String[] imgUrlList = addErrorBookNewDto.getImgUrl().trim().split(",");
        String[] idList = null;
        if (StringUtils.isNotBlank(addErrorBookNewDto.getId())) {
            idList = addErrorBookNewDto.getId().trim().split(",");
            if (idList.length == 0 || imgUrlList.length == 0 || idList.length != imgUrlList.length) {
                throw new ServiceException("请求参数格式不正确");
            }
        }

        List<String> analysisList = new ArrayList<>();
        if (ToolsKit.isNotEmpty(analysis)){
            List<String> analysisListTemp = JsonKit.jsonParseArray(analysis, String.class);
            for (String str : analysisListTemp) {
                analysisList.add(ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), str));
            }
        }
        if (ToolsKit.isNotEmpty(analysisList) && analysisList.size()>3){
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("题目解析不得超过3个");
        }

        Map<String, Object> result = new HashMap<String, Object>();
        for (int i = 0; i < imgUrlList.length; i++) {
            if (StringUtils.isBlank(imgUrlList[i])) {
                continue;
            }

            String id = "";
            if (idList != null && idList.length > i) {
                id = idList[i];
            }

            QuestionCollect questionCollect = questionCollectDao.getById(id, ToolsConst.DATA_SUCCESS_STATUS);
            String subject = null;
            if (ToolsKit.isEmpty(questionCollect)) {
                questionCollect = new QuestionCollect();
                ToolsKit.setIdEntityData(questionCollect, addErrorBookNewDto.getUserId());
                questionCollect.setUserId(addErrorBookNewDto.getUserId());
            } else {
                subject = questionCollect.getLabelId();
            }
            questionCollect.setLabelId(addErrorBookNewDto.getErrorSubject());
            questionCollect.setPic(imgUrlList[i]);
            questionCollect.setErrorType(addErrorBookNewDto.getErrorType());
            questionCollect.setErrorDiffic(addErrorBookNewDto.getErrorDiffic());
            questionCollect.setErrorReason(addErrorBookNewDto.getErrorReason());
            questionCollect.setErrorDegree(addErrorBookNewDto.getErrorDegree());
            questionCollect.setErrorSource(addErrorBookNewDto.getErrorSource());
            questionCollect.setCustomLabel(addErrorBookNewDto.getCustomLabel());
            if (ToolsKit.isNotEmpty(analysisList)){
                questionCollect.setAnalysisList(analysisList);
            }
            questionCollectDao.saveEntity(questionCollect);
            collectLabelBuService.updateCount(addErrorBookNewDto.getUserId(), addErrorBookNewDto.getErrorSubject(), true);
            if (ToolsKit.isNotEmpty(subject)) {
                collectLabelBuService.updateCount(addErrorBookNewDto.getUserId(), subject, true);
            }

            result.put("id", questionCollect.getId());
        }
        return result;
    }
}