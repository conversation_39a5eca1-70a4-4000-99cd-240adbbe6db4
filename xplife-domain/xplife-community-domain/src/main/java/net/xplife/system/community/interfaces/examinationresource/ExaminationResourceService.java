package net.xplife.system.community.interfaces.examinationresource;

import net.xplife.system.community.dto.examinationresource.ExaminationResourceDto;
import net.xplife.system.community.entity.examinationresource.ExaminationResource;
import net.xplife.system.mongo.common.Page;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface ExaminationResourceService {
    /***
     * 获取分页
     * @param pageno
     * @param pagesize
     * @param subject
     * @param examType
     * @param gradeLevel
     * @return
     */
    @RequestMapping(value = "/community/v1/examination/getpage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<ExaminationResource> findPage(@RequestParam("pageno") int pageno, @RequestParam("pagesize") int pagesize, @RequestParam("subject") String subject, @RequestParam("examType") String examType, @RequestParam("gradeLevel") String gradeLevel);

    /***
     * 删除
     * @param id
     */
    @RequestMapping(value = "/community/v1/examination/delete", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void delete(@RequestParam("id") String id);

    /**
     * 新增或修改
     * @param examinationResourceDto
     */
    @RequestMapping(value = "/community/v1/examination/saveorupdate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ExaminationResourceDto saveOrUpdate(@RequestBody ExaminationResourceDto examinationResourceDto);
}
