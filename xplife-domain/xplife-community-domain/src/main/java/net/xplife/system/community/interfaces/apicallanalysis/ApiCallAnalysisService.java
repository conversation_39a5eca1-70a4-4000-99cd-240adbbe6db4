package net.xplife.system.community.interfaces.apicallanalysis;

import net.xplife.system.community.entity.apicallanalysis.ApiCallAnalysis;
import net.xplife.system.web.core.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 微服务对外提供api服务层
 *
 * <AUTHOR>
 */
@FeignClient(name = "yoyin-community", configuration = FeignConfiguration.class)
public interface ApiCallAnalysisService {
    /***
     *
     * @return
     */
    @RequestMapping(value = "/community/v1/apicallanalysis/getapicallanalysisbetweendateandtype", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<ApiCallAnalysis> findPage(@RequestParam("businessType") String businessType,
                                          @RequestParam("beginDate") String beginDate,
                                          @RequestParam("endDate") String endDate);

    @RequestMapping(value = "/community/v1/apicallanalysis/getByUserCount", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<ApiCallAnalysis> findPage(@RequestParam("businessType") String businessType,
                                          @RequestParam("otherId") String userId,
                                          @RequestParam("beginDate") String beginDate,
                                          @RequestParam("endDate") String endDate);
}
