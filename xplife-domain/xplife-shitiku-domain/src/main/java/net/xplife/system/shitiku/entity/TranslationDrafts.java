package net.xplife.system.shitiku.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * Author：Chenjy
 * Date:2025/8/25
 * Description: 文本翻译-草稿箱
 */
@Data
@Document(collection = "V1_TranslationDrafts")
public class TranslationDrafts implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final String USER_ID_FIELD    = "userId";
    public static final String STATUS_FIELD       = "status";
    public static final String CREATETIME_FIELD   = "createtime";


    @Id
    private String id;

    /**
     * 用户ID
     */
    @NotNull
    @Indexed(name = "_userId_")
    private String userId;

    //原文
    private String original;

    //译文
    private String translation;

    private Integer status; // 状态：0-正常，1-已删除

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;
}
